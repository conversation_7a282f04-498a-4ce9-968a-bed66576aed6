/**
 * 将日期格式化为月-日格式，并判断是否为今天、明天、后天
 * @param dateStr 日期字符串，格式为 YYYY-MM-DD
 * @returns 格式化后的日期字符串
 */
function formatDateToMonthDay(dateStr) {
  // 检查输入是否有效
  if (!dateStr || dateStr.length !== 10) {
    return dateStr;
  }

  // 解析传入的日期 - WXS中需要手动解析
  var parts = dateStr.split('-');
  if (parts.length !== 3) {
    return dateStr;
  }
  
  var year = parseInt(parts[0]);
  var month = parseInt(parts[1]);
  var day = parseInt(parts[2]);
  
  // 创建日期对象
  var inputDate = getDate(year, month - 1, day);

  // 获取当前日期
  var today = getDate();
  today.setHours(0, 0, 0, 0);

  // 计算明天和后天的日期
  var tomorrow = getDate(today.getTime() + 24 * 60 * 60 * 1000);
  var dayAfterTomorrow = getDate(today.getTime() + 2 * 24 * 60 * 60 * 1000);

  // 比较日期
  var inputTime = inputDate.getTime();
  var todayTime = today.getTime();
  var tomorrowTime = tomorrow.getTime();
  var dayAfterTomorrowTime = dayAfterTomorrow.getTime();

  if (inputTime >= todayTime && inputTime < todayTime + 24 * 60 * 60 * 1000) {
    return "今天";
  } else if (inputTime >= tomorrowTime && inputTime < tomorrowTime + 24 * 60 * 60 * 1000) {
    return "明天";
  } else if (inputTime >= dayAfterTomorrowTime && inputTime < dayAfterTomorrowTime + 24 * 60 * 60 * 1000) {
    return "后天";
  } else {
    // 格式化为 MM-DD
    var monthStr = month < 10 ? '0' + month : '' + month;
    var dayStr = day < 10 ? '0' + day : '' + day;
    return monthStr + '-' + dayStr;
  }
}

module.exports = {
  formatDateToMonthDay: formatDateToMonthDay
};
