page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.result-status {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0 72rpx;
  .logo {
    width: 152rpx;
    height: 152rpx;
  }
  .status-text {
    font-size: 28rpx;
    font-weight: 500;
    margin: 24rpx 0;
  }
  .payment-amount {
    font-size: 80rpx;
    line-height: 84rpx;
    font-weight: bold;
    padding-bottom: 24rpx;
    .currency {
      font-size: 40rpx;
      font-weight: 500;
      line-height: 56rpx;
    }
  }
}

.transaction-info {
  padding: 32rpx;
  margin-top: 24rpx;
  background-color: #fff;
  .transaction-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.operation-container {
  display: flex;
  padding: 40rpx 32rpx;
  background-color: #fff;
  flex: 1;
  .launch-app {
    width: 100%;
    height: 100rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}