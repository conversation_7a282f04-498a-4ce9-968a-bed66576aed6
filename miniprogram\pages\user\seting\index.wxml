<!-- 设置页面 - 用户设置和功能入口页面 -->
<view class="seting-my-page">
  <!-- 设置功能菜单区域 -->
  <view class="seting-menu">
    <!-- 菜单项：绑定点击事件，传递页面路径 -->
    <view class="menu-item" wx:for="{{setingMenus}}" wx:key="path" data-path="{{item.path}}" bindtap="onMenuClick">
      <text class="menu-title">{{item.title}}</text>
      <image class="arrow-icon" src="{{arrowIcon}}"></image>
    </view>
  </view>
  
  <!-- 退出登录按钮区域 -->
  <view class="login-out">
    <text class="login-out-btn">退出登录</text>
  </view>
</view>