.border(@width: 1px , @style: solid, @color: #e0e0e0, @radius: 0, @side: all) {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
    pointer-events: none;
    // 处理圆角
    & when not (@radius = 0) {
      border-radius: @radius * 2;
    }
    
    // 处理边框
    .border-process() when (@side = all) {
      border: @width @style @color;
    }
    
    .border-process() when not (@side = all) {
      border-@{side}: @width @style @color;
    }
    
    .border-process();
  }
}
