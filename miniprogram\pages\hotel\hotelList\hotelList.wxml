<wxs src="../../../utils/tool.wxs" module="tool" />
<view class="hotelListPage">
  <navbar cityName="{{cityName}}" cityId="{{cityId}}" />
  <search arrivalDate="{{tool.formatDateToMonthDay(arrivalDate)}}" departureDate="{{tool.formatDateToMonthDay(departureDate)}}" queryText="{{queryText}}" bind:search="handleSearch" />
  <view class="hotelList-content">
    <!-- 酒店列表 -->
    <hotel-item wx:for="{{hotels}}" wx:key="hotelId" hotelData="{{item}}" bind:hotelClick="handleHotelClick"></hotel-item>
    <!-- 加载更多提示 -->
    <view class="load-more" wx:if="{{!hasMore}}">
      <text>已显示全部酒店</text>
    </view>
  </view>
</view>