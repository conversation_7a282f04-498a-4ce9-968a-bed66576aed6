<wxs src="../../../utils/tool.wxs" module="tool" />
<view class="hotelListPage">
  <navbar cityName="{{cityName}}" cityId="{{cityId}}" />
  <search arrivalDate="{{tool.formatDateToMonthDay(arrivalDate)}}" departureDate="{{tool.formatDateToMonthDay(departureDate)}}" queryText="{{queryText}}" bind:search="handleSearch" />
  <filter current-sort-label="{{filterData.currentSortLabel}}" price-star-count="{{filterData.priceStarCount}}" distance-count="{{filterData.distanceCount}}" filter-count="{{filterData.filterCount}}" bind:sortClick="onSortClick" bind:priceStarClick="onPriceStarClick" bind:distanceClick="onDistanceClick" bind:filterClick="onFilterClick" />
  <expand-popup visible="{{expandPopupVisible}}" bind:close="onExpandPopupClose">
    <!-- 智能排序组件 -->
    <rec-sort wx:if="{{currentFilterType === 'sort'}}" sortList="{{sortList}}" bind:sortSelect="onSortSelect" />
    <!-- 价格/星级组件 -->
    <price-star wx:if="{{currentFilterType === 'priceStar'}}" priceRangeVOs="{{priceRangeVOs}}" starRatingVOs="{{starRatingVOs}}" bind:priceStarChange="onPriceStarChange" />
    <!-- 位置距离组件 -->
    <area-distance wx:if="{{currentFilterType === 'distance'}}" areaDistance="{{areaDistance}}" bind:distanceChange="onDistanceChange" />
    <!-- 更多筛选组件 -->
    <more-filter wx:if="{{currentFilterType === 'filter'}}" filter-options="{{filterOptions}}" bind:filterChange="onFilterChange" />
  </expand-popup>
  <quick-filter />
  <scroll-view class="hotelList-content" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refresherTriggered}}" refresher-default-style="none" refresher-background="#f5f5f5" bindrefresherrefresh="onRefresherRefresh" bindscrolltolower="onScrollToLower" lower-threshold="{{50}}">
    <!-- 自定义下拉刷新内容 -->
    <view slot="refresher" class="custom-refresher">
      <t-loading wx:if="{{refreshing}}" theme="circular" size="40rpx" text="正在刷新..." />
      <view wx:else class="refresh-tip">
        <text>下拉刷新</text>
      </view>
    </view>
    <!-- 酒店列表 -->
    <hotel-item wx:for="{{hotels}}" wx:key="hotelId" hotelData="{{item}}" bind:hotelClick="handleHotelClick"></hotel-item>
    <!-- 上拉加载更多状态 -->
    <view class="load-more" wx:if="{{loadingMore}}">
      <t-loading theme="circular" size="40rpx" text="加载更多..." />
    </view>
    <!-- 没有更多数据提示 -->
    <view class="load-more" wx:elif="{{!hasMore && hotels.length > 0}}">
      <text>已显示全部酒店</text>
    </view>
  </scroll-view>
</view>