import appConfig from '../config/app.config';
import { StorageKeyEnum } from '../enum/app';

/**
 * 文件上传工具类
 * 专门处理微信小程序的文件上传请求
 */

/**
 * 上传文件的配置选项
 */
export interface UploadOptions {
  /** 接口地址 */
  url: string;
  /** 文件路径 */
  filePath: string;
  /** 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容 */
  name: string;
  /** HTTP 请求 Header */
  header?: Record<string, string>;
  /** HTTP 请求中其他额外的 form data */
  formData?: Record<string, any>;
  /** 超时时间，单位为毫秒 */
  timeout?: number;
  /** 基础URL */
  baseURL?: string;
  /** 是否忽略API错误提示 */
  ignoreApiError?: boolean;
}

/**
 * 上传响应结果
 */
export interface UploadResult<T = any> {
  /** 是否成功 */
  isSuccess: boolean;
  /** 状态码 */
  code: number;
  /** 响应数据 */
  data: T | null;
  /** 响应消息 */
  message: string;
}

/**
 * 文件上传函数
 * 
 * @param options 上传配置选项
 * @returns Promise<UploadResult<T>>
 */
export const uploadFile = <T = any>(options: UploadOptions): Promise<UploadResult<T>> => {
  return new Promise((resolve, reject) => {
    const {
      url,
      filePath,
      name,
      header = {},
      formData = {},
      timeout = appConfig.requestTimeout,
      baseURL = appConfig.baseUrl + '/api',
      ignoreApiError = false
    } = options;

    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 准备请求头部
    const uploadHeaders: Record<string, string> = { ...header };

    // 添加认证 Token
    if (token) {
      uploadHeaders['HT-Token'] = token;
    }

    console.log('=== 上传文件开始 ===');
    console.log('上传配置:', {
      url: `${baseURL}${url}`,
      filePath,
      name,
      headers: uploadHeaders,
      formData,
      timeout
    });
    console.log('Content-Type 应该是: multipart/form-data (微信自动设置)');

    // 执行文件上传
    const uploadTask = wx.uploadFile({
      url: `${baseURL}${url}`,
      filePath,
      name,
      header: uploadHeaders,
      formData,
      timeout,
      success: (res) => {
        console.log('=== 上传成功 ===');
        console.log('HTTP状态码:', res.statusCode);
        console.log('原始响应数据:', res.data);
        console.log('响应数据类型:', typeof res.data);

        try {
          // 解析响应数据
          const responseData = JSON.parse(res.data);

          console.log('解析后的响应数据:', responseData);
          console.log('响应数据结构:', {
            code: responseData.code,
            message: responseData.message,
            data: responseData.data,
            url: responseData.url
          });

          // 构造统一的响应格式
          const result: UploadResult<T> = {
            isSuccess: responseData.code === 200,
            code: responseData.code || res.statusCode,
            data: responseData.data || responseData.url || null,
            message: responseData.message || '上传成功'
          };

          // 检查是否需要显示错误提示
          if (!result.isSuccess && !ignoreApiError) {
            wx.showToast({
              title: result.message || '上传失败',
              icon: 'none'
            });
          }

          resolve(result);
        } catch (error) {
          console.error('解析上传响应失败:', error);
          const result: UploadResult<T> = {
            isSuccess: false,
            code: -1,
            data: null,
            message: '响应数据解析失败'
          };

          if (!ignoreApiError) {
            wx.showToast({
              title: result.message,
              icon: 'none'
            });
          }

          resolve(result);
        }
      },
      fail: (error) => {
        console.error('上传失败:', error);

        const result: UploadResult<T> = {
          isSuccess: false,
          code: -1,
          data: null,
          message: error.errMsg || '上传失败'
        };

        if (!ignoreApiError) {
          wx.showToast({
            title: result.message,
            icon: 'none'
          });
        }

        resolve(result);
      }
    });

    // 可以返回 uploadTask 用于取消上传等操作
    // return uploadTask;
  });
};

/**
 * 上传图片的便捷方法
 * 
 * @param filePath 图片文件路径
 * @param options 额外的上传选项
 * @returns Promise<UploadResult<{url: string}>>
 */
export const uploadImage = (
  filePath: string,
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<{ url: string }>> => {
  // 确保 url 参数存在
  if (!options.url) {
    throw new Error('url is required for uploadImage');
  }

  return uploadFile<{ url: string }>({
    filePath,
    name: 'files', // 默认参数名
    url: options.url,
    header: options.header,
    formData: options.formData,
    timeout: options.timeout,
    baseURL: options.baseURL,
    ignoreApiError: options.ignoreApiError
  });
};

/**
 * 批量上传图片（逐个上传）
 *
 * @param filePaths 图片文件路径数组
 * @param options 上传选项
 * @returns Promise<UploadResult<{url: string}>[]>
 */
export const uploadImages = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<{ url: string }>[]> => {
  const results: UploadResult<{ url: string }>[] = [];

  for (let i = 0; i < filePaths.length; i++) {
    const filePath = filePaths[i];

    try {
      const result = await uploadImage(filePath, options);
      results.push(result);

      // 如果上传失败，可以选择继续或停止
      if (!result.isSuccess) {
        console.warn(`第 ${i + 1} 张图片上传失败:`, result.message);
        // 这里可以选择 break 来停止后续上传
      }
    } catch (error) {
      console.error(`第 ${i + 1} 张图片上传异常:`, error);
      results.push({
        isSuccess: false,
        code: -1,
        data: null,
        message: '上传异常'
      });
    }
  }

  return results;
};

/**
 * 一次性上传多张图片（如果接口支持）
 * 注意：微信小程序的 wx.uploadFile 只能上传单个文件
 * 如果需要一次上传多张，需要使用特殊的实现方式
 *
 * @param filePaths 图片文件路径数组
 * @param options 上传选项
 * @returns Promise<UploadResult<{urls: string[]}>>
 */
export const uploadMultipleImages = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<{ urls: string[] }>> => {
  // 确保 url 参数存在
  if (!options.url) {
    throw new Error('url is required for uploadMultipleImages');
  }

  // 微信小程序不支持一次上传多个文件，需要逐个上传然后合并结果
  const results = await uploadImages(filePaths, options);

  // 检查是否所有上传都成功
  const successResults = results.filter(result => result.isSuccess);
  const failedResults = results.filter(result => !result.isSuccess);

  if (failedResults.length > 0) {
    // 有失败的上传
    return {
      isSuccess: false,
      code: -1,
      data: null,
      message: `${failedResults.length} 张图片上传失败`
    };
  }

  // 所有上传都成功，合并URL
  const urls = successResults.map(result => result.data?.url).filter(Boolean) as string[];

  return {
    isSuccess: true,
    code: 200,
    data: { urls },
    message: `成功上传 ${urls.length} 张图片`
  };
};

/**
 * 一次上传多个文件（使用 Base64 编码）
 *
 * @param filePaths 文件路径数组
 * @param options 上传选项
 * @returns Promise<UploadResult<string[]>>
 */
export const uploadMultipleFilesAsBase64 = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<string[]>> => {
  const {
    url,
    baseURL = appConfig.baseUrl + '/api',
    timeout = appConfig.requestTimeout,
    ignoreApiError = false
  } = options;

  if (!url) {
    throw new Error('url is required for uploadMultipleFilesAsBase64');
  }

  console.log('=== 开始多文件Base64上传 ===');
  console.log('文件数量:', filePaths.length);
  console.log('文件路径:', filePaths);

  try {
    // 将所有文件转换为 Base64
    const base64Files = await Promise.all(
      filePaths.map(async (filePath, index) => {
        try {
          const base64 = await fileToBase64(filePath);
          console.log(`文件 ${index + 1} 转换为Base64成功，长度:`, base64.length);
          return {
            filename: `image_${index + 1}.png`,
            data: base64
          };
        } catch (error) {
          console.error(`文件 ${index + 1} 转换失败:`, error);
          throw error;
        }
      })
    );

    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 准备请求头部
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['HT-Token'] = token;
    }

    // 构造请求数据
    const requestData = {
      files: base64Files
    };

    console.log('发送请求:', {
      url: `${baseURL}${url}`,
      headers,
      dataLength: JSON.stringify(requestData).length
    });

    // 发送请求
    const response = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: `${baseURL}${url}`,
        method: 'POST',
        header: headers,
        data: requestData,
        timeout,
        success: resolve,
        fail: reject
      });
    });

    console.log('多文件上传响应:', response);

    // 处理响应
    const responseData = response.data as any;
    const result: UploadResult<string[]> = {
      isSuccess: responseData.code === 200,
      code: responseData.code || response.statusCode,
      data: responseData.data || null,
      message: responseData.message || '上传成功'
    };

    if (!result.isSuccess && !ignoreApiError) {
      wx.showToast({
        title: result.message || '上传失败',
        icon: 'none'
      });
    }

    return result;

  } catch (error: any) {
    console.error('多文件上传失败:', error);

    const result: UploadResult<string[]> = {
      isSuccess: false,
      code: -1,
      data: null,
      message: error.errMsg || error.message || '上传失败'
    };

    if (!ignoreApiError) {
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }

    return result;
  }
};

/**
 * 将文件转换为 Base64
 */
const fileToBase64 = (filePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath,
      encoding: 'base64',
      success: (res) => {
        resolve(res.data as string);
      },
      fail: reject
    });
  });
};

/**
 * 尝试使用 wx.request 模拟 multipart/form-data 多文件上传
 * 注意：这种方法可能不被所有服务器支持
 */
export const uploadMultipleFilesAsFormData = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<string[]>> => {
  const {
    url,
    baseURL = appConfig.baseUrl + '/api',
    timeout = appConfig.requestTimeout,
    ignoreApiError = false
  } = options;

  if (!url) {
    throw new Error('url is required for uploadMultipleFilesAsFormData');
  }

  console.log('=== 开始多文件FormData上传 ===');
  console.log('文件数量:', filePaths.length);

  try {
    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 构造 multipart/form-data 格式的数据
    const boundary = '----WebKitFormBoundary' + Math.random().toString(36).substr(2);
    let formData = '';

    // 添加文件数据
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];
      const base64Data = await fileToBase64(filePath);

      formData += `--${boundary}\r\n`;
      formData += `Content-Disposition: form-data; name="files"; filename="image_${i + 1}.png"\r\n`;
      formData += `Content-Type: image/png\r\n\r\n`;
      formData += base64Data + '\r\n';
    }

    formData += `--${boundary}--\r\n`;

    // 准备请求头部
    const headers: Record<string, string> = {
      'Content-Type': `multipart/form-data; boundary=${boundary}`
    };

    if (token) {
      headers['HT-Token'] = token;
    }

    console.log('发送FormData请求:', {
      url: `${baseURL}${url}`,
      headers,
      dataLength: formData.length
    });

    // 发送请求
    const response = await new Promise<WechatMiniprogram.RequestSuccessCallbackResult>((resolve, reject) => {
      wx.request({
        url: `${baseURL}${url}`,
        method: 'POST',
        header: headers,
        data: formData,
        timeout,
        success: resolve,
        fail: reject
      });
    });

    console.log('多文件FormData上传响应:', response);

    // 处理响应
    const responseData = response.data as any;
    const result: UploadResult<string[]> = {
      isSuccess: responseData.code === 200,
      code: responseData.code || response.statusCode,
      data: responseData.data || null,
      message: responseData.message || '上传成功'
    };

    return result;

  } catch (error: any) {
    console.error('多文件FormData上传失败:', error);

    const result: UploadResult<string[]> = {
      isSuccess: false,
      code: -1,
      data: null,
      message: error.errMsg || error.message || '上传失败'
    };

    if (!ignoreApiError) {
      wx.showToast({
        title: result.message,
        icon: 'none'
      });
    }

    return result;
  }
};
