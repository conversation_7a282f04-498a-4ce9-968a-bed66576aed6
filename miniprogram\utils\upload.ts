import { appConfig } from '../config/app.config';
import { StorageKeyEnum } from '../enum/storage';
import { showToast } from './ui';

/**
 * 文件上传工具类
 * 专门处理微信小程序的文件上传请求
 */

/**
 * 上传文件的配置选项
 */
export interface UploadOptions {
  /** 接口地址 */
  url: string;
  /** 文件路径 */
  filePath: string;
  /** 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容 */
  name: string;
  /** HTTP 请求 Header */
  header?: Record<string, string>;
  /** HTTP 请求中其他额外的 form data */
  formData?: Record<string, any>;
  /** 超时时间，单位为毫秒 */
  timeout?: number;
  /** 基础URL */
  baseURL?: string;
  /** 是否忽略API错误提示 */
  ignoreApiError?: boolean;
}

/**
 * 上传响应结果
 */
export interface UploadResult<T = any> {
  /** 是否成功 */
  isSuccess: boolean;
  /** 状态码 */
  code: number;
  /** 响应数据 */
  data: T | null;
  /** 响应消息 */
  message: string;
}

/**
 * 文件上传函数
 * 
 * @param options 上传配置选项
 * @returns Promise<UploadResult<T>>
 */
export const uploadFile = <T = any>(options: UploadOptions): Promise<UploadResult<T>> => {
  return new Promise((resolve, reject) => {
    const {
      url,
      filePath,
      name,
      header = {},
      formData = {},
      timeout = appConfig.requestTimeout,
      baseURL = appConfig.baseUrl + '/api',
      ignoreApiError = false
    } = options;

    // 获取认证 Token
    const token = wx.getStorageSync(StorageKeyEnum.Token);

    // 准备请求头部
    const uploadHeaders: Record<string, string> = { ...header };

    // 添加认证 Token
    if (token) {
      uploadHeaders['HT-Token'] = token;
    }

    console.log('上传文件配置:', {
      url: `${baseURL}${url}`,
      filePath,
      name,
      headers: uploadHeaders,
      formData
    });

    // 执行文件上传
    const uploadTask = wx.uploadFile({
      url: `${baseURL}${url}`,
      filePath,
      name,
      header: uploadHeaders,
      formData,
      timeout,
      success: (res) => {
        console.log('上传成功原始响应:', res);

        try {
          // 解析响应数据
          const responseData = JSON.parse(res.data);

          console.log('解析后的响应数据:', responseData);

          // 构造统一的响应格式
          const result: UploadResult<T> = {
            isSuccess: responseData.code === 200,
            code: responseData.code || res.statusCode,
            data: responseData.data || responseData.url || null,
            message: responseData.message || '上传成功'
          };

          // 检查是否需要显示错误提示
          if (!result.isSuccess && !ignoreApiError) {
            showToast({
              title: result.message || '上传失败',
            });
          }

          resolve(result);
        } catch (error) {
          console.error('解析上传响应失败:', error);
          const result: UploadResult<T> = {
            isSuccess: false,
            code: -1,
            data: null,
            message: '响应数据解析失败'
          };

          if (!ignoreApiError) {
            showToast({
              title: result.message,
            });
          }

          resolve(result);
        }
      },
      fail: (error) => {
        console.error('上传失败:', error);

        const result: UploadResult<T> = {
          isSuccess: false,
          code: -1,
          data: null,
          message: error.errMsg || '上传失败'
        };

        if (!ignoreApiError) {
          showToast({
            title: result.message,
          });
        }

        resolve(result);
      }
    });

    // 可以返回 uploadTask 用于取消上传等操作
    // return uploadTask;
  });
};

/**
 * 上传图片的便捷方法
 * 
 * @param filePath 图片文件路径
 * @param options 额外的上传选项
 * @returns Promise<UploadResult<{url: string}>>
 */
export const uploadImage = (
  filePath: string,
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<{ url: string }>> => {
  return uploadFile<{ url: string }>({
    filePath,
    name: 'files', // 默认参数名
    ...options
  });
};

/**
 * 批量上传图片
 * 
 * @param filePaths 图片文件路径数组
 * @param options 上传选项
 * @returns Promise<UploadResult<{url: string}>[]>
 */
export const uploadImages = async (
  filePaths: string[],
  options: Partial<UploadOptions> = {}
): Promise<UploadResult<{ url: string }>[]> => {
  const results: UploadResult<{ url: string }>[] = [];

  for (let i = 0; i < filePaths.length; i++) {
    const filePath = filePaths[i];

    try {
      const result = await uploadImage(filePath, options);
      results.push(result);

      // 如果上传失败，可以选择继续或停止
      if (!result.isSuccess) {
        console.warn(`第 ${i + 1} 张图片上传失败:`, result.message);
        // 这里可以选择 break 来停止后续上传
      }
    } catch (error) {
      console.error(`第 ${i + 1} 张图片上传异常:`, error);
      results.push({
        isSuccess: false,
        code: -1,
        data: null,
        message: '上传异常'
      });
    }
  }

  return results;
};
