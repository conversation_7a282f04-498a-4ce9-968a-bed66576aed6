import { getImageUrl } from "../../../utils/images";

// pages/user/accountCancellationStep1/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    circleBlueIcon: getImageUrl('user/circle_blue.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    agreementChecked: false, // 协议是否勾选
  },
  
  onToggleAgreement() {
    this.setData({ agreementChecked: !this.data.agreementChecked });
  },
  
  onNextStep() {
    if (!this.data.agreementChecked) {
      wx.showToast({
        title: '请先阅读并同意注销协议',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到第二步页面
    wx.navigateTo({
      url: '/pages/user/accountCancellationStep2/index'
    });
  }
})
