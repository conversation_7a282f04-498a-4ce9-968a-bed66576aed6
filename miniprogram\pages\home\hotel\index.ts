import { getNearbyHotels } from "../../../api/home";
import { ComponentWithStore } from 'mobx-miniprogram-bindings';
import { store } from '../../../store/store';

ComponentWithStore({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    hotelList: [] as Home.IRecommendHotel[]
  },

  storeBindings: {
    store,
    fields: ['geoInfo', 'cityInfo'],
    actions: []
  } as const,

  lifetimes: {
    attached() {
      const { lat, lng } = this.data.geoInfo;
      getNearbyHotels({
        page: 1,
        size: 10,
        businessType: 1,
        domesticIntl: 1,
        cityName: this.data.cityInfo.cityName,
        latitude: lat,
        longitude: lng
      }).then(res => {
        if (res.isSuccess) {
          this.setData({
            hotelList: res.data?.records ?? []
          })
        }
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})