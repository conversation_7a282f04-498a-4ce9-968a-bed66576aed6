import { getImageUrl } from "../../../utils/images";

// pages/user/accountCancellation/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    circleBlueIcon: getImageUrl('user/circle_blue.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    code: '',
    focus: false,
    step: 1, // 当前步骤 1-2
    agreementChecked: false, // 协议是否勾选
  },
  onInput(e: any) {
    let value = e.detail.value.replace(/\D/g, '').slice(0, 6);
    this.setData({ code: value });
  },
  onFocus() {
    this.setData({ focus: true });
  },
  onBlur() {
    this.setData({ focus: false });
  },
  onTapCodeBox() {
    this.setData({ focus: true });
  },
  onToggleAgreement() {
    this.setData({ agreementChecked: !this.data.agreementChecked });
  },
  onNextStep() {
    if (this.data.agreementChecked) {
      this.setData({ step: 2 });
    }
  },
  onSubmit() {
    // 这里可补充注销提交逻辑
    wx.showToast({ title: '注销已提交', icon: 'success' });
  },
})