import { request } from "../../utils/request";

/**
 * 获取酒店列表
 */
export const hotelListApi = (data: Hotel.IHotelListParams) => request<Hotel.IHotelListResponse>({
  url: "/hotel/search/list/v1",
  method: "POST",
  data
})

export const sortTypeApi = () => request<Hotel.ISortTypeList>({
  url: "/hotel/search/metadata/v1",
  method: "GET"
})

export const priceStarApi = () => request<Hotel.IPriceStarFilterResponse>({
  url: "/hotel/search/filter/getRateStar/v1",
  method: "GET"
})

export const areaApi = ( data: Hotel.IAreaFilterParams ) => request<Hotel.IAreaFilterResponse>({
  url: `/hotel/search/filter/getDistrictFilters/v1?cityId=${data.cityId}&typeId=${data.typeId}`,
  method: "GET",
  data
})

export const filterOptionsApi = ( cityId: string ) => request<Hotel.IFilterOptionsResponse>({
  url: `/hotel/search/filter/getFilters/v1?cityId=${cityId}`,
  method: "GET",
})

/**
 * 获取微信支付参数
 * @param data 请求参数
 */
export const getPaymentParams = (data: Pay.IGetPayParams) => request<Pay.IWxPayTrade<Pay.ITradeInfo>>({
  url: "/hotel/payment/lianLianPay/v1/invocationPayment",
  method: "POST",
  data
})

/**
 * 查询支付状态
 * @param orderNo 订单号
 */
export const queryPayStatus = (orderNo: string) => request<Pay.IWxPayTrade<Omit<Pay.ITradeInfo, "payload">>>({
  url: "/hotel/payment/lianLianQuery/v1/queryPaymentStatus",
  method: "POST",
  ignoreApiError: true,
  data: {
    orderNo
  }
});

