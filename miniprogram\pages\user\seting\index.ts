import { getImageUrl } from "../../../utils/images";

// pages/user/seting/index.ts
Page({
  data: {
    setingMenus: [{
      title: '个人资料',
      path: '/pages/user/userInfo/index'
    }, {
      title: '关于我们',
      path: '/pages/user/aboutUs/index'
    }, {
      title: '账号注销',
      path: '/pages/user/accountCancellationStart/index'
    }],
    // 箭头图标，用于列表项右侧
    arrowIcon: getImageUrl('arrow.png'),
  },
  /**
   * 处理菜单项点击事件
   * @param e 事件对象
   */
  onMenuClick(e: any) {
    const { path } = e.currentTarget.dataset;

    if (path) {
      // 如果是跳转到个人资料页面，添加事件监听
      if (path === '/pages/user/userInfo/index') {
        wx.navigateTo({
          url: path,
          events: {
            // 监听userInfo页面的用户信息更新事件
            updateUserInfo: (data: My.UserInfoUpdateEvent) => {
              // 转发事件给my页面
              const eventChannel = this.getOpenerEventChannel();
              if (eventChannel) {
                eventChannel.emit('updateUserInfo', data as My.UserInfoUpdateEvent);
              }
            }
          },
          fail: (err) => {
            console.error('页面跳转失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        return
      }
      // 其他页面正常跳转
      wx.navigateTo({
        url: path,
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },
})