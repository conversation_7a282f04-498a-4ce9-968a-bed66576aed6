<!-- 意见反馈页面 -->
<view class="feedback-page">
  <!-- 意见类型 -->
  <view class="feedback-type-section">
    <view class="section-title">意见类型</view>
    <view class="type-options">
      <view wx:for="{{feedbackTypes}}" wx:key="value" class="type-option {{selectedType === item.value ? 'selected' : ''}}" bindtap="onSelectType" data-value="{{item.value}}">
        {{item.label}}
      </view>
    </view>
  </view>

  <!-- 详细意见 -->
  <view class="feedback-content-section">
    <view class="section-title">详细意见</view>
    <view class="content-input-wrapper">
      <textarea class="content-input" placeholder="请详细描述您的意见和建议，我们会更好的为您服务" value="{{feedbackContent}}" bindinput="onContentInput" maxlength="500" auto-height />
      <view class="char-count">{{feedbackContent.length}}/500</view>
    </view>
  </view>

  <!-- 上传图片 -->
  <view class="upload-section">
    <view class="section-title">上传图片{{uploadedImages.length}}/9</view>
    <view class="upload-grid">
      <!-- 已上传的图片 -->
      <view wx:for="{{uploadedImages}}" wx:key="index" class="image-item">
        <image src="{{item}}" class="uploaded-image" mode="aspectFill" />
        <view class="delete-btn" bindtap="onDeleteImage" data-index="{{index}}">
          <image class="close-icon" src="{{feedbackCloseIcon}}"></image>
        </view>
      </view>

      <!-- 上传按钮 -->
      <view wx:if="{{uploadedImages.length < 9}}" class="upload-btn" bindtap="onChooseImage">
        <image class="picture-icon" src="{{pictureIcon}}"></image>
        <view class="upload-text">上传图片</view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <text class="submit-btn" bindtap="onSubmit" disabled="{{!canSubmit}}">
      提交
    </text>
  </view>
</view>