/* pages/hotelList/hotelList.wxss */
/* 酒店列表样式 */
.hotelListPage {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

.hotelList-content {
    flex: 1;
    height: 0; /* 关键：给scroll-view明确的高度基础 */
    padding: 0 16rpx;
    box-sizing: border-box;
    /* scroll-view会自动处理滚动，移除overflow-y */
}

.load-more {
    text-align: center;
    padding:10rpx 0 40rpx 0;
    color: #999;
    font-size: 28rpx;
}

/* 自定义下拉刷新样式 */
.custom-refresher {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 100%;
    
    .refresh-tip {
        color: #666;
        font-size: 28rpx;
    }
}