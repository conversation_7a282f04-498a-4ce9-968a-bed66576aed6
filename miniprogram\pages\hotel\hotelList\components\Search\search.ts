// pages/hotelList/components/Search/search.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 默认入住日期
    arrivalDate: {
      type: String,
      value: '03.25'
    },
    // 默认离店日期
    departureDate: {
      type: String,
      value: '03.27'
    },
    // 搜索关键词
    queryText: {
      type: String,
      value: '上海迪士尼乐园'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 可以在这里添加组件内部数据
    imgBaseUrl: getApp().globalData.imgBaseUrl,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理日期选择点击事件
     */
    handleDateSelect() {
      // 触发日期选择事件
      this.triggerEvent('dateSelect', {
        arrivalDate: this.properties.arrivalDate,
        departureDate: this.properties.departureDate
      });
    },

    /**
     * 处理搜索框点击事件
     */
    handleSearch() {
      wx.navigateTo({
        url: '/pages/hotel/jump/jump?queryText=' + this.properties.queryText
      });
      // 触发搜索框点击事件
      this.triggerEvent('search', {
        queryText: this.properties.queryText
      });
      console.log('handleSearch', this.properties.queryText);
    },

    /**
     * 处理地图按钮点击事件
     */
    handleMapClick() {
      // 触发地图按钮点击事件
      wx.navigateTo({
        url: '/pages/hotel/map/map'
      });
    },

    /**
     * 处理收藏按钮点击事件
     */
    handleFavoriteClick() {
      // 触发收藏按钮点击事件
      wx.navigateTo({
        url: '/pages/hotel/favorite/favorite'
      });
    }
  }
})