declare global {
	type Nullable<T> = T | null
	/// <reference path="./types/index.d.ts" />
	
	interface IAccountInfo {
		openid: string;
		unionid: string;
	}
	
	interface IAppOption {
		globalData: {
			userInfo?: WechatMiniprogram.UserInfo,
			initTask: Nullable<Promise<IAccountInfo>>,
			imgBaseUrl: string,
		}
		userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
		init: () => Promise<{
			openid: string;
			unionid: string;
		}>;	
	}

	interface ILocation {
		/** 经度 */
		longitude: number | string;
		/** 纬度 */
		latitude: number | string;
	}

	interface ICity {
		/** 城市id */
		cityId: string;
		/** 城市名称 */
		cityName: string;
	}
}

export {}
