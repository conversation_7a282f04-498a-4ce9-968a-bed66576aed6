# 项目说明
该项目使用了微信官方的原生[Typescript](https://www.typescriptlang.org/docs/)模板 + mobx-miniprogram + miniprogram-computed与构建方式

## `环境与依赖安装`

首先确保已拥有 Node 环境，建议使用版本 `v16.x.x`。

在根目录下安装项目依赖：

```bash
npm install
```

接着需要构建小程序依赖 `miniprogram_npm`，可以通过微信开发者工具进行操作：选择 `工具` ---> `构建npm`。

![](/Users/<USER>/workspace/dreame/wx-mini-ota/docs/npm-build.png)

## `项目目录结构`

```
wx-mini-ota  项目根目录
├─ miniprogram          小程序主要代码
│  ├─ api               接口
│  ├─ components        公共组件
│  ├─ config            配置
│  ├─ enum              枚举定义
│  ├─ core              核心库
│  ├─ pages             小程序页面目录
│  │   ├─ pay           支付
│  │   ├─ index         首页
│  │   ├─ user          个人中心
│  │   ├─ hotel         酒店          
│  │   ├─ flight        机票          
│  │   └─ train         火车票          
│  ├─ static            静态资源
│  ├─ styles            scss 变量、函数、原子类等
│  ├─ utils             工具函数
│  ├─ app.json          全局配置文件
│  ├─ app.scss          全局样式文件
│  ├─ app.ts            全局逻辑文件
│  └─ sitemap.json      网站地图配置文件
├─ project.config.json  项目配置文件
├─ tsconfig.json        TypeScript配置文件
└─ typings              全局 ts 定义目录（仅复用的类型需要定义在这里）
```

## `Typescript 使用说明`

官方文档：[Typescript](https://www.typescriptlang.org/docs/)

项目中已经包含微信小程序最新的 types 定义(`WechatMiniprogram`) 见项目 `typings/wx/` 下的文件（虽然是官方最新的 ts 声明，但相比实际的 api 差别仍然很大，可自行补充）。

### 类型定义

Page、Component 的类型定义，不需要特意去写类型定义，自身的类型推断就足够了

### 自定义属性

> 不参与渲染的数据及非响应式数据，ts封装在了customInstanceProperty节点上并用 `as 关键字` 进行类型约束，以便于Component构造器做类型推导，即使this可以随意赋值如果不进行类型声明，使用的时候类型会为any需要做类型约束，失去了用ts的意义。eg：计时器标识存储

```typescript
Page({
  ...
  customInstanceProperty: {
    __timer: null as Nullable<number>
  },
  ...
  onLoad() {
  	this.__timer = setInterval(() => {
  		// todo something
		}, 1000)
	},
  onUnload() {
    this.__timer && clearInterval(this.__timer);
  }
})
```




### request工具类使用

接口的类型定义，需定义接口传参，跟响应体内容，例如：

```ts
/**
 * 有接口 `/api/test`
 *
 * 接口传参为:
 * {  userId: string }
 *
 * 接口响应内容格式：
 * {
 *    code: number,
 *    data: { value: number },
 *    message: string
 * }
 *
 * 接口代码如下：
 */
import { request } from "../utils/request";

export const getTestValue = async (
  /** 泛型 Request.IRequestOption 的第一个参数，为 request.data 的类型  */
  options?: Request.IRequestOption<{
    /** 用户 id */
    userId: number;
  }>
) =>
  /**
   * request 函数会返回 Promise<Request.IResponseResult<Result>>,
   * request 函数的泛型参数会作为 Result 传入返回的结果
   * */
  request<{
    /** 跳到认证页面 1跳到审核中页面 3跳到认证失败页面4去首页 */
    value: number;
  }>({
    url: "/api/test",
    ...options,
  });

// 使用的时候
import { getTestValue } from "../api/demo"

Page({
  async onLoad() {
    const res = await getTestValue({ userId: '123' });
    if (res.isSuccess) {
      // 成功流程
    } else {
      // 处理异常 res.code/ res.message
    }
  }
})
```

## `Mobx 状态管理`

状态管理使用了官方适配的 小程序版 mobx [mobx-miniprogram](https://github.com/wechat-miniprogram/mobx)@6+以及[mobx-miniprogram-bindings](https://github.com/wechat-miniprogram/mobx-miniprogram-bindings)@5的最新版本

项目中仅将需要复用且参与页面渲染的状态存到 store 中（`miniprogram/store/index`）

Page 和 Component 的状态绑定有所差异，推荐如下使用

> 注意⚠️：mobx的计算属性千万不要使用，会导致数据失去响应式，推荐使用小程序官方提供的[miniprogram-computed](https://github.com/wechat-miniprogram/computed)实现

```typescript
// 计算属性错误示例：失去响应式，mobx的bug
// import { observable, action } from 'mobx-miniprogram';

/**
const store = observable({
  firstName: 'kevin',
  lastName: 'durant',
  get fullName() {
    return this.firstname + this.lastName;
  }
})
*/
```

Component

> 注意末尾需要加上 `as const` 以便更好的类型推导！！！

```typescript
import { ComponentWithStore } from "mobx-miniprogram-bindings"

ComponentWithStore({
  ...
  storeBindings: {
    store,
    fields: ["numA", "numB", "sum"],
    actions: {
      update: 'update'
    }
  } as const
  ...
})
```

![](./docs/component-usage.png)

Page

> 由于Page构造器没有提供很好的类型推断，推荐业务组件化，尽量在Component中使用状态管理

```typescript
/**
*	由于mobx对Page的支持不尽人意，使用过程中没有类型推到，只能自己集成类型声明
*	集成声明要注意尽量使用data原始数据的类型推导再拼上mobx的数据，而不是声明整个data对象
* actions通过拼接TCustomInstanceProperty来实现
*/

import { connectMobx, store, Store } from '../../../store/index'

const initData = {
  value: 0,
  obj: {
    name: ''
  }
}

type Data = typeof initData & Pick<Store, 'numA' | 'numB'>

Page<Data, WechatMiniprogram.Page.CustomOption & {}, {
  update: () => void
}>({
  data: 
  ...
  behaviors: [
    connectMobx({
      fields: ["numA", "numB"],
      actions: ["update"]
    })
  ],
  ...
  onLoad() {
    this.update();
  }
})
```

## miniprogram-computed

> 类似Vue Framework的computed/React Framework的useMemo，根据依赖数据重新执行计算。[文档地址](https://github.com/wechat-miniprogram/computed)
>
> 即使miniprogram-computed和mobx-miniprogram都出自微信小程序官方，两者都做了typescript的支持ComponentWidthStore/ComponentWidthComputed，但尴尬的是日常业务中两者常常结合来使用，结合起来使用的话会顾此失彼，类型推断不能同时兼容，所以重写了Copmonent，若同时使用computed+mobx请使用/core/componentWithStoreComputed。
>
> **推荐：将页面组件化，Page暂时不支持自动类型推导，如果要使用的话，需要手动声明类型实现。**

### Page和Component中只使用mobx请参考上文Mobx 状态管理

### Component中使用computed

> 参考官方文档[miniprogram-computed](https://github.com/wechat-miniprogram/computed)实现

```typescript
import { ComponentWithComputed } from 'miniprogram-computed'

ComponentWithComputed({
  data: {
    a: 1,
    b: 1,
    sum: 2,
  },
  watch: {
    'a, b': function (a, b) {
      this.setData({
        sum: a + b,
      })
    },
  },
  computed: {
    sum(data) {
      // 注意： computed 函数中不能访问 this ，只有 data 对象可供访问
      // 这个函数的返回值会被设置到 this.data.sum 字段中
      return data.a + data.b + data.sum // data.c 为自定义 behavior 数据段
    },
  },
})
```

### Component中使用computed+mobx

> 此种方式千万不要用ComponentWithStore和ComponentWithComputed，请使用/core/componentWithStoreComputed实现
>
> **使用注意⚠️：**
>
> **1、behaviors 注入storeBindingsBehavior和computedBehavior**
>
> **2、as const关键字便于类型推导**

![componentwidthstorecomputed](/Users/<USER>/workspace/dreame/wx-mini-ota/docs/componentwidthstorecomputed.png)


## `1px 边框`

> UI 的效果图是针对 750px 出的，小程序刚好用 750 个 rpx 将屏幕宽度分为 750 份。效果图中经常出现 1px 的边框，理所应当会想到用 1rpx 绘制。但不尽人意，小程序下使用 1rpx 绘制边框，IOS 部分机型上会有边框缺失，而 Android 机型上边框比较粗。项目中使用 after 伪类绘制 1px 边框，并用 translate:scale(0.5) 对 1px 边框进行 0.5 倍缩放，这一不太完美的方式来实现 1px 边框（因为实际屏幕的 dpr 比可能不是 2...）。

miniprogram/styles/mixins.scss 中定义了 `border mixin`，可传入 5 个参数（均为可选参数，有默认值，实际以项目中为准），分别为：边框宽度、边框样式、边框颜色、边框圆角、边框方向

关于圆角有以下几点需要注意：

1. 因为整体缩放了 0.5 倍，所以边框中的圆角，正常是需要传入 2 倍值，例如：效果图中为 16px，需传入 32rpx，但是工具类底层实现了这一逻辑，使用时正常传1倍值即可。
2. 因为圆角是给伪类加的，所以元素自身也要加圆角。例如：效果图中为 16px，圆角为 16rpx

使用方式：less 文件中直接通过 `@import "./styles/mixins.less"` 使用 mixin

```less
@import "../../../styles/mixins.less";

.line {
	.border(1px, solid, #02C397, 20rpx, top); 
}
```

## `wx.showLoading和wx.showToast冲突问题`
> 小程序官方文档中[wx.showLoading](https://developers.weixin.qq.com/miniprogram/dev/api/ui/interaction/wx.showLoading.html)介绍到[wx.showLoading](https://developers.weixin.qq.com/miniprogram/dev/api/ui/interaction/wx.showLoading.html) 和 [wx.showToast](https://developers.weixin.qq.com/miniprogram/dev/api/ui/interaction/wx.showToast.html) 同时只能显示一个，在项目中调用showToast会导致shouLoading提前关闭，试想一下业务中可能会使用shouLoading来做防抖，这个特性会导致防抖被打破，因为在项目中reqeust工具类中封装了接口异常toast，所以在页面中切记一定不要使用wx.showLoading来做防抖，改为使用/components/loading/loading组件来实现loading避免与原生toast冲突。

使用方式

xx.json 注册组件`"md-loading": "../../../components/loading/loading"`

xx.wxml	`<md-loading visible="{{loading}}" />`

xx.ts中正常setData更新loading就可以了