import { getImageUrl } from "../../../utils/images";

// 注销协议H5页面URL
const LOGOUT_AGREEMENT_URL = 'http://webapp.test.aitrip123.com/agreements/user';

// pages/user/accountCancellationStart/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    circleBlueIcon: getImageUrl('user/circle_blue.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    agreementChecked: false, // 协议是否勾选
  },
  
  onToggleAgreement() {
    this.setData({ agreementChecked: !this.data.agreementChecked });
  },

  /**
   * 点击注销协议，跳转到H5页面
   */
  onAgreementClick() {
    wx.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(LOGOUT_AGREEMENT_URL)}&title=${encodeURIComponent('注销协议')}`
    });
  },
  
  onNextStep() {
    if (!this.data.agreementChecked) {
      wx.showToast({
        title: '请先阅读并同意注销协议',
        icon: 'none'
      });
      return;
    }

    // 跳转到验证页面
    wx.navigateTo({
      url: '/pages/user/accountCancellationVerify/index'
    });
  }
})
