import { getImageUrl } from "../../../utils/images";
import { sendLogoutCode } from "../../../api/my";

// pages/user/accountCancellationStart/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    circleBlueIcon: getImageUrl('user/circle_blue.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    agreementChecked: false, // 协议是否勾选
  },
  
  onToggleAgreement() {
    this.setData({ agreementChecked: !this.data.agreementChecked });
  },
  
  async onNextStep() {
    if (!this.data.agreementChecked) {
      wx.showToast({
        title: '请先阅读并同意注销协议',
        icon: 'none'
      });
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '发送验证码中...'
    });

    try {
      // 调用发送注销验证码接口
      const { isSuccess, message } = await sendLogoutCode();

      // 隐藏加载提示
      wx.hideLoading();

      if (isSuccess) {
        // 发送成功，跳转到验证页面
        wx.showToast({
          title: '验证码已发送',
          icon: 'success',
          duration: 1500
        });

        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/user/accountCancellationVerify/index'
          });
        }, 1500);
      } else {
        // 发送失败，显示错误信息
        wx.showToast({
          title: message || '发送验证码失败',
          icon: 'none'
        });
      }
    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();
      console.error('发送验证码失败:', error);
      wx.showToast({
        title: '发送验证码失败，请重试',
        icon: 'none'
      });
    }
  }
})
