
/** 性别枚举 */
export enum GenderEnum {
    /** 未知 */
    Unknown = '0',
    /** 男 */
    Male = '1',
    /** 女 */
    Female = '2'
  }
  
  /** 性别文本枚举 */
  export enum GenderTextEnum {
    /** 保密 */
    Secret = '保密',
    /** 男 */
    Male = '男',
    /** 女 */
    Female = '女'
  }

  /** 反馈类型枚举 */
  export enum EFeedbackType {
    /** 功能故障 */
    BUG = 1,
    /** 产品建议 */
    SUGGESTION = 2,
    /** 订单问题 */
    ORDER = 3,
    /** 其他反馈 */
    OTHER = 4
  }