/* 设置页面样式 - 用户设置和功能入口页面样式 */
.seting-my-page {

  /* 设置功能菜单样式 - 圆角卡片设计 */
  .seting-menu {
    margin: 16rpx;
    border-radius: 40rpx;
    background-color: #fff;

    /* 菜单项样式 - 水平布局，左右对齐 */
    .menu-item {
      display: flex;
      justify-items: space-between;
      align-items: center;
      padding: 0 32rpx;
      margin-bottom: 8rpx;
      height: 104rpx;

      /* 菜单标题样式 - 占据剩余空间 */
      .menu-title {
        flex: 1;
        font-size: 28rpx;
        color: #000;
      }

      /* 箭头图标样式 - 右侧指示箭头 */
      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  /* 退出登录按钮样式 - 圆角按钮设计 */
  .login-out {
    margin: 0 16rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    background: #FFFFFF;
    border-radius: 120rpx;
  }
}