import { store } from '../../../../store/index';
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings';
import { behavior as computedBehavior } from 'miniprogram-computed';
import { ComponentWithComputedStore } from '../../../../core/componentWithStoreComputed';

const RADIUS = 4;
const INIT_MARKER = {
	customCallout: {
    display: 'ALWAYS',
    content: '腾讯总部'
  },
  id: 900000,
	latitude: 40.040415,
  longitude: 116.273511,
  active: true,
	width: 0,
	height: 0,
	rotate: 0,
  alpha: 0
};
const INIT_CALLOUT = {
	content: '￥675起',
	padding: 12,
	display: 'ALWAYS',
	fontSize: 14,
	textAlign: 'center',
	borderRadius: RADIUS,
	borderWidth: 2,
	bgColor: '#ffffff'
};
const INIT_CALLOUT_MARKER = {
	callout: {
		...INIT_CALLOUT
	},
	latitude: 40.040415,
	longitude: 116.273511,
};

ComponentWithComputedStore({
  /**
   * 组件的属性列表
   */
  behaviors: [storeBindingsBehavior, computedBehavior],
  properties: {
    info: {
      type: Object,
      value: {
        unionid: '',
        openid: '',
      },
    },
  },
  storeBindings: {
    store,
    fields: ['geoInfo'],
    actions: [],
  } as const,

  /**
   * 组件的初始数据
   */
  data: {
    val: 0,
    markers: [{
			...INIT_MARKER
		}, {
      customCallout: {
        display: 'ALWAYS',
        content: '腾讯总部2'
      },
      id: 900001,
      latitude: 40.040515,
      longitude: 116.283511,
      width: 0,
      height: 0,
      rotate: 0,
      alpha: 0
    }],
		calloutMarkers: [{
			...INIT_CALLOUT_MARKER
		}],
    scale: 15,
    location: {
			latitude: 40.040415,
			longitude: 116.273511
		},
  },
  lifetimes: {
    ready() {
      this.data.geoInfo;
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleCalloutTap(e: WechatMiniprogram.CustomEvent<{ markerId: string}>) {
      const markId = e.detail.markerId
      debugger
    },
  },
});
