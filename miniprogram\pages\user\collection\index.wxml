<!--pages/user/collection/index.wxml-->
<view class="collection-page">

  <!-- Tab切换 -->
  <view class="tab-container">
    <t-tabs
      value="{{currentTab}}"
      list="{{tabs}}"
      bind:change="onTabChange"
      theme="line"
      placement="top"
    />
  </view>

  <!-- 编辑模式下的全选按钮 -->
  <view class="select-all-container" wx:if="{{isEditMode}}">
    <view class="select-all-btn" bindtap="onToggleSelectAll">
      <image
        class="checkbox-icon"
        src="{{getImageUrl(isAllSelected ? 'circleActIcon' : 'circleIcon')}}"
        mode="aspectFit"
      />
      <text class="select-all-text">全选</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view class="content-container" scroll-y enhanced>
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <t-loading theme="circular" size="40rpx" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 列表内容 -->
    <view class="list-container" wx:else>
      <!-- 收藏列表 -->
      <view
        class="item-wrapper"
        wx:for="{{favoriteList}}"
        wx:key="id"
        wx:if="{{currentTab === 'favorite'}}"
      >
        <view 
          class="item-container {{currentSwipeIndex === index ? 'swiped' : ''}}"
          data-index="{{index}}"
          data-id="{{item.id}}"
          bindtouchstart="onTouchStart"
          bindtouchmove="onTouchMove"
          bindtouchend="onTouchEnd"
          bindtap="onItemClick"
        >
          <!-- 选择框（编辑模式） -->
          <view class="checkbox-container" wx:if="{{isEditMode}}">
            <image
              class="checkbox-icon"
              src="{{getImageUrl(selectedItems.includes(item.id) ? 'circleActIcon' : 'circleIcon')}}"
              mode="aspectFit"
            />
          </view>

          <!-- 酒店卡片内容 -->
          <view class="hotel-card">
            <!-- 酒店图片 -->
            <view class="hotel-image">
              <image src="{{item.image}}" mode="aspectFill" />
            </view>

            <!-- 酒店信息 -->
            <view class="hotel-info">
              <view class="hotel-header">
                <text class="hotel-name">{{item.name}}</text>
                <text class="hotel-type">{{item.type}}</text>
              </view>

              <view class="hotel-rating">
                <text class="rating-score">{{item.rating}}分</text>
                <view class="stars">
                  <t-icon name="star-filled" size="12" color="#FFB800" />
                  <t-icon name="star-filled" size="12" color="#FFB800" />
                  <t-icon name="star-filled" size="12" color="#FFB800" />
                  <t-icon name="star-filled" size="12" color="#FFB800" />
                  <t-icon name="star-filled" size="12" color="#FFB800" />
                </view>
              </view>

              <view class="hotel-location">
                <text class="location">{{item.location}}</text>
                <text class="subway">{{item.subway}}</text>
              </view>

              <view class="hotel-amenities">
                <text 
                  class="amenity-tag"
                  wx:for="{{item.amenities}}"
                  wx:key="*this"
                  wx:for-item="amenity"
                >{{amenity}}</text>
              </view>

              <view class="hotel-price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{item.price}}</text>
                <text class="price-unit">起</text>
              </view>
            </view>
          </view>

          <!-- 右滑删除按钮 -->
          <view class="delete-btn" data-id="{{item.id}}" bindtap="onDeleteItem">
            <image class="close-icon" src="{{deleteIcon}}"></image>
            <text class="delete-text">删除</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" wx:if="{{favoriteList.length === 0 && currentTab === 'favorite' && !loading}}">
        <t-icon name="heart" size="80" color="#E0E0E0" />
        <text class="empty-text">暂无收藏</text>
        <text class="empty-desc">快去收藏你喜欢的酒店吧</text>
      </view>

      <!-- 其他Tab的占位内容 -->
      <view class="placeholder-container" wx:if="{{currentTab !== 'favorite'}}">
        <text class="placeholder-text">{{currentTab}}功能开发中...</text>
      </view>
    </view>
  </scroll-view>

  <!-- 悬浮按钮 -->
  <view class="floating-btn" wx:if="{{favoriteList.length > 0}}">
    <!-- 编辑模式：显示取消按钮 -->
    <view wx:if="{{isEditMode}}" class="floating-cancel-btn" bindtap="onBatchCancel">
      <t-icon name="close" size="24" color="#fff" />
      <text class="floating-btn-text">取消</text>
    </view>
    <!-- 普通模式：显示编辑按钮 -->
    <view wx:else class="floating-edit-btn" bindtap="onToggleEditMode">
      <t-icon name="edit" size="24" color="#fff" />
      <text class="floating-btn-text">编辑</text>
    </view>
  </view>

  <!-- 编辑模式下的底部操作栏 -->
  <view class="bottom-actions" wx:if="{{isEditMode}}">
    <view class="action-info">
      <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
    </view>
    <view class="action-buttons">
      <button class="cancel-btn" bindtap="onBatchCancel" disabled="{{selectedItems.length === 0}}">
        取消收藏
      </button>
    </view>
  </view>
</view>
