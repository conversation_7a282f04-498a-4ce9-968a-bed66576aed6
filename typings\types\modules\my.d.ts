import { ESmsType } from "../../../miniprogram/enum"

declare namespace My {
  /**
   * 用户信息接口返回数据结构
   * 包含用户的基本信息，如头像、昵称、生日等
   */
  interface IUserInfoRes {
    /** 用户头像URL，为空时使用默认头像 */
    avatar: string
    /** 用户生日，格式：YYYY-MM-DD */
    birthday: string
    /** 用户邮箱 */
    email: string
    /** 用户性别：0-未知，1-男，2-女 */
    gender: string
    /** 用户手机号 */
    mobile: string
    /** 用户昵称 */
    nickname: string
    /** 用户ID */
    userId: number
  }
  /**
   * 用户信息事件传递类型
   * avatar事件只带avatar，nickname事件只带nickname
   */
  type UserInfoUpdateEvent =
    | ({ type: 'avatar' } & Pick<IUserInfoRes, 'avatar'>)
    | ({ type: 'nickname' } & Pick<IUserInfoRes, 'nickname'>);
  /**
   * 更新用户信息请求数据结构
   * 从 IUserInfoRes 中排除系统字段和敏感信息
   */
  type IUpdateUserInfoReq = Omit<IUserInfoRes, 'email' | 'mobile' | 'userId'>

  // 注销接口入参
  interface IFetchLogoutReq {
    // 验证码
    smsCode: string
    // 验证码类型
    smsType: ESmsType
  }
}
