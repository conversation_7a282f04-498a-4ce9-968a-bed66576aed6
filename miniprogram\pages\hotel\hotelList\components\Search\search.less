/* pages/hotelList/components/Search/search.wxss */

.search-container {
  background-color: #fff;
  padding: 16rpx 20rpx;
}

.search-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 搜索框样式（包含日期选择和搜索输入） */
.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 54rpx;
  height: 108rpx;
  margin-right: 10rpx;
  padding: 0 40rpx;
}

/* 日期选择区样式 */
.date-selector {
  margin-right: 15rpx;
  font-size: 20rpx;
}

.date-item {
  align-items: center;
  justify-content: center;
  color: #11111E;
}

.date-label {
  display: inline-block;
  margin-right: 12rpx;
  width: 30rpx;
}

.date-separator {
  width: 2rpx;
  height: 40rpx;
  background-color: #ccc;
  margin: 0 20rpx 0 16rpx;
}

/* 搜索输入区样式 */
.search-input {
  flex: 1;
  display: flex;
  align-items: center;
}

.search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  position: relative;
}

/* 使用伪元素创建搜索图标 */
.search-icon:before {
  content: "";
  position: absolute;
  top: 1rpx;
  left: 1rpx;
  width: 18rpx;
  height: 18rpx;
  border: 2rpx solid #999;
  border-radius: 50%;
}

.search-icon:after {
  content: "";
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 10rpx;
  height: 2rpx;
  background: #999;
  transform: rotate(45deg);
  transform-origin: right bottom;
}

.search-placeholder {
  font-size: 28rpx;
  color: #CCCCCE;
}

.search-keyword {
  font-size: 28rpx;
  color: #11111E;
}

.search-queryText {
  font-size: 28rpx;
  color: #11111E;
  width:330rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧按钮组 */
.action-buttons {
  display: flex;
  align-items: center;
}

.map-btn, .favorite-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

/* 地图和收藏图标 */
.map-icon, .favorite-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-text {
  font-size: 20rpx;
  color: #333;
  margin-top: 4rpx;
}