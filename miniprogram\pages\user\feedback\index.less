/* 意见反馈页面样式 */
.feedback-page {
  background-color: #fff;
  min-height: 100vh;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

/* 意见类型选择 */
.feedback-type-section {
  padding: 48rpx 24rpx;
}

.type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.type-option {
  padding: 16rpx 28rpx;
  background-color: #F7F7F7;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #11111E;

  &.selected {
    background-color: #568ded;
    color: #fff;
  }
}

/* 详细意见输入 */
.feedback-content-section {
  margin-top: 96rpx;
  padding: 0 24rpx;
}

.content-input-wrapper {
  background-color: #F7F7F7;
  border-radius: 16rpx;
  padding: 32rpx;
  position: relative;
}

.content-input {
  width: 100%;
  min-height: 300rpx;
  max-height: 300rpx;
  font-size: 28rpx;
  color: #000;
  line-height: 1.6;
  border: none;
  outline: none;
  resize: none;
  overflow-y: auto;
}

.char-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 28rpx;
  color: #CCCCCE;
}

/* 上传图片 */
.upload-section {
  padding:  0 24rpx;
  margin-top: 96rpx;
}

.upload-grid {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 16rpx;
  margin-top: 32rpx;
  -webkit-overflow-scrolling: touch;
}

.image-item {
  position: relative;
  width: 148rpx;
  height: 148rpx;
  margin-right: 16rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 32rpx;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 0rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-icon{
    width: 100%;
    height: 100%;
  }
}

.delete-icon {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

.upload-btn {
  flex: 0 0 148rpx;
  width: 148rpx;
  height: 148rpx;
  background: #f7f7f7;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.picture-icon {
  height: 48rpx;
  width: 48rpx;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #66666e;
}

/* 提交按钮 */
.submit-section{
  margin: 96rpx 16rpx 0;
}
.submit-btn {
  height: 112rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-color: #568DED;
  border-radius: 120rpx;
  width: 100%;
  font-size: 32rpx;
  display: inline-block;
  color: #fff;
}