/* 我的页面样式 - 用户个人中心主页面样式 */
.user-my-page {
  /* 头部区域样式 - 包含背景图和用户信息展示 */
  .head {
    height: 630rpx;
    /* 顶部内边距，确保内容不被状态栏遮挡 */
    padding-top: 88rpx;
    /* 背景图居中显示并拉伸填充 */
    background-position: center;
    background-size: 100% 100%;
    
    /* 设置按钮容器 - 预留设置按钮位置 */
    .seting {
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    
    /* 设置图标样式 */
    .seting-icon {
      margin-right: 40rpx;
      height: 48rpx;
      width: 48rpx;
    }
    
    /* 未登录状态样式 - 居中显示登录提示和按钮 */
    .no-login {
      width: 100%;
      flex-direction: column;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      /* 未登录提示文字样式 */
      .no-login-text {
        font-size: 40rpx;
        color: #fff;
        margin-bottom: 40rpx;
        font-weight: bold;
      }
      
      /* 登录按钮样式 - 圆角按钮设计 */
      .no-login-button {
        font-size: 32rpx;
        width: 336rpx;
        line-height: 120rpx;
        height: 120rpx;
        color: #fff;
        border-radius: 120rpx;
        display: inline-block;
        text-align: center;
        background-color: #568ded;
      }
    }
  }
  
  /* 用户信息区域样式 - 已登录状态下显示头像和昵称 */
  .info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /* 顶部内边距，确保信息显示在背景图合适位置 */
    padding-top: 454rpx;
    padding-left: 40rpx;
    padding-bottom: 48rpx;
    
    /* 用户头像样式 - 圆形头像 */
    .info-icon {
      width: 118rpx;
      height: 118rpx;
    }
    
    /* 用户昵称样式 - 白色文字 */
    .info-text {
      padding-left: 16rpx;
      text-align: left;
      color: #fff;
      font-size: 28rpx;
    }
  }
  
  /* 标题区域样式 - 订单管理标题 */
  .title {
    padding: 32rpx;
    height: 44rpx;
    line-height: 44rpx;
    
    /* 标题文字样式 */
    .text {
      color: #000;
      font-size: 32rpx;
    }
  }
  
  /* 订单状态快捷入口样式 - 四个订单状态按钮 */
  .order-tab {
    margin-bottom: 80rpx;
    display: flex;
    padding: 0 44rpx;
    
    /* 订单状态项样式 - 等分布局 */
    .order-tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      /* 状态图标样式 */
      .order-tab-icon {
        width: 64rpx;
        height: 64rpx;
      }
      
      /* 状态文字样式 */
      .order-tab-title {
        margin-top: 12rpx;
        color: #33333e;
        font-size: 24rpx;
      }
    }
  }

  /* 用户功能菜单样式 - 收藏、反馈、设置等功能入口 */
  .user-menu {
    margin: 0 32rpx;
    
    /* 菜单项样式 - 圆角卡片设计 */
    .menu-item {
      background-color: #fff;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      padding: 0 32rpx;
      margin-bottom: 8rpx;
      height: 104rpx;

      /* 菜单图标样式 */
      .menu-icon {
        width: 40rpx;
        height: 40rpx;
      }

      /* 菜单标题样式 - 占据剩余空间 */
      .menu-title {
        margin-left: 24rpx;
        flex: 1;
        font-size: 28rpx;
        color: #000;
      }

      /* 箭头图标样式 - 右侧指示箭头 */
      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}
