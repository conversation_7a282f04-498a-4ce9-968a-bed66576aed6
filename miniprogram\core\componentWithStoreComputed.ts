type FilterUnknownType<T> = WechatMiniprogram.Component.FilterUnknownType<T>;

type UnionStoreData<T extends IStoreBindings<any>[], Result = unknown> =
  T extends [infer First extends IStoreBindings<any>, ...infer Rest extends IStoreBindings<any>[]]
    ? UnionStoreData<Rest, Result & StoreData<First>>
    : Result;

type UnionStoreAction<T extends IStoreBindings<any>[], Result = unknown> =
  T extends [infer First extends IStoreBindings<any>, ...infer Rest extends IStoreBindings<any>[]]
    ? UnionStoreAction<Rest, Result & StoreAction<First>>
    : Result;

type Action = string;
export interface IStoreBindings<T extends Record<string, any>> {
    namespace?: string;
    store: T;
    fields: (keyof T)[] | {
        [k: string]: (keyof T | ((...args: any) => any));
    };
    actions: (keyof T)[] | {
        [k: Action]: keyof T;
    };
    structuralComparison?: boolean;
}

type StoreData<T extends IStoreBindings<any>> = T['fields'] extends string[] ? {
		[k in T['fields'][number]]: T['store'][k];
} : T['fields'] extends {
		[k: Action]: string | ((...args: any) => any);
} ? {
		[k in keyof T['fields']]: (T['fields'][k] extends (...args: any) => any ? ReturnType<T['fields'][k]> : T['fields'][k] extends string ? T['store'][T['fields'][k]] : unknown);
} : unknown;
type StoreAction<T extends IStoreBindings<any>> = T['actions'] extends string[] ? {
		[k in T['actions'][number]]: T['store'][k];
} : T['actions'] extends {
		[k: Action]: string;
} ? {
		[k in keyof T['actions']]: T['store'][T['actions'][k]];
} : unknown;

type AllDataAndProperties<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption
> = FilterUnknownType<TData> & WechatMiniprogram.Component.MixinData<TBehavior> & WechatMiniprogram.Component.MixinProperties<TBehavior> & WechatMiniprogram.Component.PropertyOptionToData<FilterUnknownType<TProperty>>;

type StoreComputedInstance<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any>,
	TCustomProperty extends WechatMiniprogram.IAnyObject = Record<string, never>
> = WechatMiniprogram.Component.Instance<TData & {
    [k in keyof TComputed]: ReturnType<TComputed[k]>;
}, TProperty, TMethod, TBehavior, TCustomProperty>;

type StoreComputedOptions<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any>,
	TWatch extends Record<string, (...args: any[]) => void>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any>,
	TCustomInstanceProperty extends WechatMiniprogram.IAnyObject = {}
> = (Partial<WechatMiniprogram.Component.Data<TData>> & Partial<WechatMiniprogram.Component.Property<TProperty>> & Partial<WechatMiniprogram.Component.Method<TMethod>> & Partial<WechatMiniprogram.Component.Behavior<TBehavior>> & Partial<WechatMiniprogram.Component.OtherOption> & Partial<WechatMiniprogram.Component.Lifetimes> & Partial<WechatMiniprogram.Component.CustomInstanceProperty<TCustomInstanceProperty>> & {
    watch?: TWatch;
    computed?: TComputed;
    template?: string;
} & {
	storeBindings?: TStoreBindings
}) & ThisType<StoreComputedInstance<TData & StoreData<TStoreBindings>, TProperty, TMethod & StoreAction<TStoreBindings>, TBehavior, TStoreBindings, TComputed, TCustomInstanceProperty>>;

type StoreComputedListOptions<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any>[],
	TWatch extends Record<string, (...args: any[]) => void>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any>,
	TCustomInstanceProperty extends WechatMiniprogram.IAnyObject = {}
> = (Partial<WechatMiniprogram.Component.Data<TData>> & Partial<WechatMiniprogram.Component.Property<TProperty>> & Partial<WechatMiniprogram.Component.Method<TMethod>> & Partial<WechatMiniprogram.Component.Behavior<TBehavior>> & Partial<WechatMiniprogram.Component.OtherOption> & Partial<WechatMiniprogram.Component.Lifetimes> & Partial<WechatMiniprogram.Component.CustomInstanceProperty<TCustomInstanceProperty>> & {
    watch?: TWatch;
    computed?: TComputed;
    template?: string;
} & {
		storeBindings: TStoreBindings;
}) & ThisType<WechatMiniprogram.Component.Instance<TData & UnionStoreData<TStoreBindings> & {
    [k in keyof TComputed]: ReturnType<TComputed[k]>;
}, TProperty, TMethod & UnionStoreAction<TStoreBindings>, TBehavior, TCustomInstanceProperty>>;

export declare function ComponentWithStoreComputed<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any>,
	TWatch extends Record<string, (...args: any[]) => void>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any> = {},
	TCustomInstanceProperty extends WechatMiniprogram.IAnyObject = {}
>(options: StoreComputedOptions<TData, TProperty, TMethod, TBehavior, TStoreBindings, TWatch, TComputed, TCustomInstanceProperty>): string;

export declare function ComponentWithStoreComputed<
	TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any>[],
	TWatch extends Record<string, (...args: any[]) => void>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any> = {},
	TCustomInstanceProperty extends WechatMiniprogram.IAnyObject = {}
>(options: StoreComputedListOptions<TData, TProperty, TMethod, TBehavior, TStoreBindings, TWatch, TComputed, TCustomInstanceProperty>): string;

export const ComponentWithComputedStore: <TData extends WechatMiniprogram.Component.DataOption,
	TProperty extends WechatMiniprogram.Component.PropertyOption,
	TMethod extends WechatMiniprogram.Component.MethodOption,
	TBehavior extends WechatMiniprogram.Component.BehaviorOption,
	TStoreBindings extends IStoreBindings<any> | IStoreBindings<any>[],
	TWatch extends Record<string, (...args: any[]) => void>,
	TComputed extends Record<string, (data: AllDataAndProperties<TData & (TStoreBindings extends IStoreBindings<any> ? StoreData<TStoreBindings> : TStoreBindings extends IStoreBindings<any>[] ? UnionStoreData<TStoreBindings> : never), TProperty, TBehavior>) => any> = {},
	TCustomInstanceProperty extends WechatMiniprogram.IAnyObject = {}
>(options: (
	TStoreBindings extends IStoreBindings<any> ?
		StoreComputedOptions<TData, TProperty, TMethod, TBehavior, TStoreBindings, TWatch, TComputed, TCustomInstanceProperty>
		: TStoreBindings extends IStoreBindings<any>[] ? StoreComputedListOptions<TData, TProperty, TMethod, TBehavior, TStoreBindings, TWatch, TComputed, TCustomInstanceProperty> : never)) => string = (options) => Component(options);
