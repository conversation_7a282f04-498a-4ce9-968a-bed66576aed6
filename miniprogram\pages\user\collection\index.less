/* pages/user/collection/index.less */
.collection-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  /* Tab切换 */
  .tab-container {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
  }



  /* 内容区域 */
  .content-container {
    flex: 1;
    padding: 0 16px;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;

    .loading-text {
      margin-top: 16px;
      font-size: 14px;
      color: #999;
    }
  }

  /* 列表容器 */
  .list-container {
    padding: 16px 0;
  }

  /* 项目包装器 */
  .item-wrapper {
    margin-bottom: 16px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    background-color: #fff;
  }

  /* 项目容器 */
  .item-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    transition: transform 0.3s ease;

    &.swiped {
      transform: translateX(-80px);
    }

    .checkbox-container {
      padding: 0 16px;
      display: flex;
      align-items: center;
    }
  }

  /* 复选框图标样式 */
  .checkbox-icon {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;

    /* 点击效果 */
    &:active {
      transform: scale(0.95);
    }
  }

  /* 酒店卡片 */
  .hotel-card {
    flex: 1;
    display: flex;
    padding: 16px;

    .hotel-image {
      width: 120px;
      height: 90px;
      border-radius: 8px;
      overflow: hidden;
      margin-right: 16px;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .hotel-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .hotel-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .hotel-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-right: 8px;
        }

        .hotel-type {
          font-size: 12px;
          color: #999;
          background-color: #f5f5f5;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .hotel-rating {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .rating-score {
          font-size: 14px;
          color: #333;
          margin-right: 8px;
        }

        .stars {
          display: flex;
          gap: 2px;
        }
      }

      .hotel-location {
        margin-bottom: 8px;

        .location,
        .subway {
          font-size: 14px;
          color: #666;
          margin-right: 8px;
        }
      }

      .hotel-amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-bottom: 8px;

        .amenity-tag {
          font-size: 12px;
          color: #666;
          background-color: #f5f5f5;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .hotel-price {
        display: flex;
        align-items: baseline;

        .price-symbol {
          font-size: 14px;
          color: #1976d2;
        }

        .price-value {
          font-size: 20px;
          font-weight: 600;
          color: #1976d2;
        }

        .price-unit {
          font-size: 14px;
          color: #1976d2;
          margin-left: 2px;
        }
      }
    }
  }

  /* 右滑删除按钮 */
  .delete-btn {
    position: absolute;
    right: -80px;
    top: 0;
    bottom: 0;
    width: 80px;
    background-color: #ff4757;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: right 0.3s ease;

    .item-container.swiped & {
      right: 0;
    }

    .delete-text {
      font-size: 12px;
      color: #fff;
      margin-top: 4px;
    }
  }

  /* 空状态 */
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 0;

    .empty-text {
      font-size: 16px;
      color: #999;
      margin: 16px 0 8px;
    }

    .empty-desc {
      font-size: 14px;
      color: #ccc;
    }
  }

  /* 占位内容 */
  .placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 80px 0;

    .placeholder-text {
      font-size: 16px;
      color: #999;
    }
  }

  /* 悬浮按钮 */
  .floating-btn {
    position: fixed;
    right: 24px;
    bottom: 100px;
    z-index: 100;

    /* 编辑按钮 */
    .floating-edit-btn {
      width: 56px;
      height: 56px;
      background-color: #1976d2;
      border-radius: 28px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
      transition: all 0.3s ease;

      .floating-btn-text {
        font-size: 10px;
        color: #fff;
        margin-top: 2px;
      }

      &:active {
        transform: scale(0.95);
        background-color: #1565c0;
      }
    }

    /* 取消按钮 */
    .floating-cancel-btn {
      width: 56px;
      height: 56px;
      background-color: #ff4757;
      border-radius: 28px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
      transition: all 0.3s ease;

      .floating-btn-text {
        font-size: 10px;
        color: #fff;
        margin-top: 2px;
      }

      &:active {
        transform: scale(0.95);
        background-color: #e84057;
      }
    }
  }

  /* 底部操作栏 */
  .bottom-actions {
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    /* 左侧：全选按钮 */
    .action-left {
      .select-all-btn {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.3s ease;

        &:active {
          background-color: #f0f0f0;
        }

        .checkbox-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }

        .select-all-text {
          font-size: 16px;
          color: #333;
        }
      }
    }

    /* 右侧：操作按钮 */
    .action-right {
      .cancel-btn {
        background-color: #ff4757;
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 16px;
        transition: all 0.3s ease;

        &:active:not([disabled]) {
          background-color: #e84057;
          transform: scale(0.98);
        }

        &[disabled] {
          background-color: #ddd;
          color: #999;
          cursor: not-allowed;
        }
      }
    }
  }
}
