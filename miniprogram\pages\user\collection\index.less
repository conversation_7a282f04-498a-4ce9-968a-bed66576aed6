/* pages/user/collection/index.less */
.collection-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  /* Tab切换 */
  .tab-container {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 内容区域 */
  .content-container {
    flex: 1;
    padding: 0 32rpx;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;

    .loading-text {
      margin-top: 32rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  /* 列表容器 */
  .list-container {
    padding: 32rpx 0;
  }

  /* 项目包装器 */
  .item-wrapper {
    margin-bottom: 32rpx;
    position: relative;
    overflow: hidden;
    border-radius: 24rpx;
    background-color: #fff;
  }

  /* 项目容器 */
  .item-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    transition: transform 0.3s ease;

    &.swiped {
      transform: translateX(-160rpx);
    }

    .checkbox-container {
      padding: 0 32rpx;
      display: flex;
      align-items: center;
    }
  }

  /* 复选框图标样式 */
  .checkbox-icon {
    width: 48rpx;
    height: 48rpx;
    transition: all 0.3s ease;

    /* 点击效果 */
    &:active {
      transform: scale(0.95);
    }
  }

  /* 酒店卡片 */
  .hotel-card {
    flex: 1;
    display: flex;
    padding: 32rpx;
    border-radius: 40rpx;
    .hotel-image {
      width: 240rpx;
      height: 180rpx;
      border-radius: 16rpx;
      overflow: hidden;
      margin-right: 32rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .hotel-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .hotel-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .hotel-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-right: 16rpx;
        }

        .hotel-type {
          font-size: 24rpx;
          color: #999;
          background-color: #f5f5f5;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }

      .hotel-rating {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .rating-score {
          font-size: 28rpx;
          color: #333;
          margin-right: 16rpx;
        }

        .stars {
          display: flex;
          gap: 4rpx;
        }
      }

      .hotel-location {
        margin-bottom: 16rpx;

        .location,
        .subway {
          font-size: 28rpx;
          color: #666;
          margin-right: 16rpx;
        }
      }

      .hotel-amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin-bottom: 16rpx;

        .amenity-tag {
          font-size: 24rpx;
          color: #666;
          background-color: #f5f5f5;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }

      .hotel-price {
        display: flex;
        align-items: baseline;

        .price-symbol {
          font-size: 28rpx;
          color: #1976d2;
        }

        .price-value {
          font-size: 40rpx;
          font-weight: 600;
          color: #1976d2;
        }

        .price-unit {
          font-size: 28rpx;
          color: #1976d2;
          margin-left: 4rpx;
        }
      }
    }
  }

  /* 右滑删除按钮 */
  .delete-btn {
    border-radius: 0rpx 40rpx 40rpx 0rpx;
    position: absolute;
    right: -160rpx;
    top: 0;
    bottom: 0;
    width: 160rpx;
    background-color: #fff1f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: right 0.3s ease;

    .item-container.swiped & {
      right: 0;
    }
    .close-icon {
      height: 36rpx;
      width: 36rpx;
    }
    .delete-text {
      font-size: 24rpx;
      color: #f04838;
      margin-top: 8rpx;
    }
  }

  /* 空状态 */
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 0;

    .empty-text {
      font-size: 32rpx;
      color: #999;
      margin: 32rpx 0 16rpx;
    }

    .empty-desc {
      font-size: 28rpx;
      color: #ccc;
    }
  }

  /* 占位内容 */
  .placeholder-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 160rpx 0;

    .placeholder-text {
      font-size: 32rpx;
      color: #999;
    }
  }

  /* 悬浮按钮 */
  .floating-btn {
    position: fixed;
    right: 48rpx;
    bottom: 200rpx;
    z-index: 100;

    /* 编辑按钮 */
    .floating-edit-btn {
      width: 112rpx;
      height: 112rpx;
      background-color: #1976d2;
      border-radius: 56rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
      transition: all 0.3s ease;

      .floating-btn-text {
        font-size: 20rpx;
        color: #fff;
        margin-top: 4rpx;
      }

      &:active {
        transform: scale(0.95);
        background-color: #1565c0;
      }
    }

    /* 取消按钮 */
    .floating-cancel-btn {
      width: 112rpx;
      height: 112rpx;
      background-color: #ff4757;
      border-radius: 56rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
      transition: all 0.3s ease;

      .floating-btn-text {
        font-size: 20rpx;
        color: #fff;
        margin-top: 4rpx;
      }

      &:active {
        transform: scale(0.95);
        background-color: #e84057;
      }
    }
  }

  /* 底部操作栏 */
  .bottom-actions {
    background-color: #fff;
    border-top: 2rpx solid #f0f0f0;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    /* 左侧：全选按钮 */
    .action-left {
      .select-all-btn {
        display: flex;
        align-items: center;
        padding: 16rpx 24rpx;
        border-radius: 12rpx;
        transition: background-color 0.3s ease;

        &:active {
          background-color: #f0f0f0;
        }

        .checkbox-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }

        .select-all-text {
          font-size: 32rpx;
          color: #333;
        }
      }
    }

    /* 右侧：操作按钮 */
    .action-right {
      .cancel-btn {
        background-color: #ff4757;
        color: #fff;
        border: none;
        border-radius: 16rpx;
        padding: 24rpx 48rpx;
        font-size: 32rpx;
        transition: all 0.3s ease;

        &:active:not([disabled]) {
          background-color: #e84057;
          transform: scale(0.98);
        }

        &[disabled] {
          background-color: #ddd;
          color: #999;
          cursor: not-allowed;
        }
      }
    }
  }
}
