/* pages/user/collection/index.less */
.collection-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: #fff;
  padding-top: var(--status-bar-height, 44px);
  border-bottom: 1px solid #f0f0f0;
  
  .navbar-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    
    .navbar-left {
      width: 60px;
      display: flex;
      align-items: center;
    }
    
    .navbar-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .navbar-right {
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .edit-btn, .cancel-btn {
        font-size: 16px;
        color: #1976d2;
      }
    }
  }
}

/* Tab切换 */
.tab-container {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  
  .tab-list {
    display: flex;
    padding: 0 16px;
    
    .tab-item {
      flex: 1;
      position: relative;
      padding: 16px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .tab-text {
        font-size: 16px;
        color: #666;
        transition: color 0.3s;
      }
      
      .tab-indicator {
        position: absolute;
        bottom: 0;
        width: 24px;
        height: 3px;
        background-color: #1976d2;
        border-radius: 2px;
      }
      
      &.active .tab-text {
        color: #1976d2;
        font-weight: 600;
      }
    }
  }
}

/* 全选按钮 */
.select-all-container {
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  .select-all-btn {
    display: flex;
    align-items: center;
    
    .select-all-text {
      margin-left: 8px;
      font-size: 16px;
      color: #333;
    }
  }
}

/* 内容区域 */
.content-container {
  flex: 1;
  padding: 0 16px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  
  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    color: #999;
  }
}

/* 列表容器 */
.list-container {
  padding: 16px 0;
}

/* 项目包装器 */
.item-wrapper {
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  background-color: #fff;
}

/* 项目容器 */
.item-container {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  transition: transform 0.3s ease;
  
  &.swiped {
    transform: translateX(-80px);
  }
  
  .checkbox-container {
    padding: 0 16px;
    display: flex;
    align-items: center;
  }
}

/* 复选框样式 */
.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  
  &.checked {
    background-color: #1976d2;
    border-color: #1976d2;
  }
}

/* 酒店卡片 */
.hotel-card {
  flex: 1;
  display: flex;
  padding: 16px;
  
  .hotel-image {
    width: 120px;
    height: 90px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 16px;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .hotel-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    .hotel-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .hotel-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-right: 8px;
      }
      
      .hotel-type {
        font-size: 12px;
        color: #999;
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
    
    .hotel-rating {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .rating-score {
        font-size: 14px;
        color: #333;
        margin-right: 8px;
      }
      
      .stars {
        display: flex;
        gap: 2px;
      }
    }
    
    .hotel-location {
      margin-bottom: 8px;
      
      .location, .subway {
        font-size: 14px;
        color: #666;
        margin-right: 8px;
      }
    }
    
    .hotel-amenities {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-bottom: 8px;
      
      .amenity-tag {
        font-size: 12px;
        color: #666;
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }
    
    .hotel-price {
      display: flex;
      align-items: baseline;
      
      .price-symbol {
        font-size: 14px;
        color: #1976d2;
      }
      
      .price-value {
        font-size: 20px;
        font-weight: 600;
        color: #1976d2;
      }
      
      .price-unit {
        font-size: 14px;
        color: #1976d2;
        margin-left: 2px;
      }
    }
  }
}

/* 右滑删除按钮 */
.delete-btn {
  position: absolute;
  right: -80px;
  top: 0;
  bottom: 0;
  width: 80px;
  background-color: #ff4757;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: right 0.3s ease;
  
  .item-container.swiped & {
    right: 0;
  }
  
  .delete-text {
    font-size: 12px;
    color: #fff;
    margin-top: 4px;
  }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  
  .empty-text {
    font-size: 16px;
    color: #999;
    margin: 16px 0 8px;
  }
  
  .empty-desc {
    font-size: 14px;
    color: #ccc;
  }
}

/* 占位内容 */
.placeholder-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  
  .placeholder-text {
    font-size: 16px;
    color: #999;
  }
}

/* 悬浮编辑按钮 */
.floating-btn {
  position: fixed;
  right: 24px;
  bottom: 100px;
  width: 56px;
  height: 56px;
  background-color: #1976d2;
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  z-index: 100;
}

/* 底部操作栏 */
.bottom-actions {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .action-info {
    .selected-count {
      font-size: 14px;
      color: #666;
    }
  }
  
  .action-buttons {
    .cancel-btn {
      background-color: #ff4757;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-size: 16px;
      
      &[disabled] {
        background-color: #ddd;
        color: #999;
      }
    }
  }
}
