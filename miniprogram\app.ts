import { getOpenId } from './utils/userInfo';

App<IAppOption>({
  globalData: {
    initTask: null,
    imgBaseUrl: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota',
  },
  onLaunch() {
    this.init();
  },
  async init() {
    const initTask = this.globalData.initTask;
    if (initTask) return initTask;

    this.globalData.initTask = getOpenId();
    return this.globalData.initTask;
  },
});