import { getImageUrl } from "../../../utils/images";

// pages/user/accountCancellationVerify/index.ts
Page({
  data: {
    infoIcon: getImageUrl('user/info.png'),
    accountIcon: getImageUrl('user/account.png'),
    accountArrowIcon: getImageUrl('user/account_arrow.png'),
    code: '',
    focus: false,
    countdown: 59, // 倒计时秒数
  },
  
  timer: null as any,
  
  onLoad() {
    // 开始倒计时
    this.startCountdown();
  },
  
  onUnload() {
    // 页面卸载时清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  
  startCountdown() {
    this.timer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        });
      } else {
        clearInterval(this.timer);
      }
    }, 1000);
  },
  
  onInput(e: any) {
    let value = e.detail.value.replace(/\D/g, '').slice(0, 6);
    this.setData({ code: value });
  },
  
  onFocus() {
    this.setData({ focus: true });
  },
  
  onBlur() {
    this.setData({ focus: false });
  },
  
  onTapCodeBox() {
    this.setData({ focus: true });
  },
  
  onSubmit() {
    if (this.data.code.length !== 6) {
      wx.showToast({
        title: '请输入6位验证码',
        icon: 'none'
      });
      return;
    }
    
    // 这里可补充注销提交逻辑
    wx.showModal({
      title: '确认注销',
      content: '注销后账号无法找回，确定要注销吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({ 
            title: '注销已提交', 
            icon: 'success' 
          });
          // 可以在这里添加实际的注销API调用
          setTimeout(() => {
            wx.navigateBack({ delta: 2 }); // 返回到上上级页面
          }, 1500);
        }
      }
    });
  }
})
