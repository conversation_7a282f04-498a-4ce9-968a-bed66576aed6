// console.info("exec js:\t", "store.behavior");

import { BehaviorWithStore } from "mobx-miniprogram-bindings";
import { Actions, State } from "./typings";
import { store } from "./store";

const DEFAULT_FIELDS: (keyof State)[] = []

/** 连接 Mobx */
export const connectMobx = ({
  fields = [],
  actions = [],
}: {
  /** 需要 connet 的 state */
  fields?: (keyof State)[];
  /** 需要 connet 的 action */
  actions?: (keyof Actions)[];
}) => {
  return BehaviorWithStore({
    storeBindings: {
      store,
      fields: DEFAULT_FIELDS.concat(fields || []),
      actions
    } as const
  });
};
