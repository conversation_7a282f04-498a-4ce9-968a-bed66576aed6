<!-- 个人资料页面 - 用户信息编辑和管理页面 -->
<view class="userInfo-my-page">
  <!-- 用户信息编辑区域 -->
  <view class="userInfo">
    <!-- 头像编辑项 -->
    <view class="avatar userInfo-item">
      <text class="userInfo-title">头像</text>
      <view class="userInfo-content" bindtap="onAvatarClick">
        <image class="userInfo-avatar" src="{{userInfo.avatar}}"></image>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 昵称编辑项 -->
    <view class="nickname userInfo-item">
      <text class="userInfo-title">昵称</text>
      <view class="userInfo-content" bindtap="onNicknameClick">
        <!-- 条件渲染：昵称为空时显示灰色样式 -->
        <text class="userInfo-text {{!userInfo.nickname ? 'userInfo-text-empty' : ''}}">{{userInfo.nickname || ''}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 性别编辑项 -->
    <view class="gender userInfo-item">
      <text class="userInfo-title">性别</text>
      <view class="userInfo-content" bindtap="onGenderClick">
        <!-- 根据性别显示状态动态应用样式类 -->
        <text class="userInfo-text {{genderDisplay.className}}">{{genderDisplay.text}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
    
    <!-- 生日编辑项 -->
    <view class="birthday userInfo-item">
      <text class="userInfo-title">生日</text>
      <view class="userInfo-content" bindtap="onBirthdayClick">
        <!-- 条件渲染：生日为空时显示"未设置"和灰色样式 -->
        <text class="userInfo-text {{!userInfo.birthday ? 'userInfo-text-empty' : ''}}">{{userInfo.birthday || '未设置'}}</text>
        <image class="arrow-icon" src="{{arrowIcon}}"></image>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮区域 -->
  <view class="login-out">
    <text class="login-out-btn">退出登录</text>
  </view>
</view>

<!-- 性别选择器组件 - 使用TDesign组件库 -->
<t-picker
  visible="{{genderPickerVisible}}"
  title="选择性别"
  cancelBtn="取消"
  confirmBtn="确认"
  value="{{genderPickerValue}}"
  bindcancel="onGenderPickerCancel"
  bindconfirm="onGenderPickerConfirm"
>
  <t-picker-item options="{{genderOptions}}" />
</t-picker>

<!-- 生日选择器组件 - 日期模式，限制选择范围 -->
<t-date-time-picker
  visible="{{birthdayPickerVisible}}"
  title="选择生日"
  cancelBtn="取消"
  confirmBtn="确认"
  mode="date"
  value="{{birthdayPickerValue}}"
  start="{{birthdayStartDate}}"
  end="{{birthdayEndDate}}"
  bindcancel="onBirthdayPickerCancel"
  bindconfirm="onBirthdayPickerConfirm"
/>

<!-- 昵称修改底部弹出层 - 使用TDesign弹出层组件 -->
<t-popup
  visible="{{ showNicknamePopup }}"
  placement="bottom"
  bind:overlay-click="onCancelNickname"
  bind:close="onCancelNickname"
  t-class-content="nickname-popup"
>
  <view class="nickname-popup-content">
    <!-- 弹窗头部：标题 -->
    <view class="popup-header">
      <text class="popup-title">修改昵称</text>
      <!-- 关闭按钮（已注释） -->
      <!-- <text class="close-icon" bind:tap="onCancelNickname">X</text> -->
    </view>

    <!-- 昵称输入区域：包含输入框和字符计数 -->
    <view class="nickname-input-container">
      <input
        class="nickname-input"
        value="{{ newNickname }}"
        placeholder="请输入昵称"
        maxlength="15"
        bind:input="onNicknameChange"
        bind:focus="onNicknameInputFocus"
        bind:blur="onNicknameInputBlur"
      />
      <!-- 字符计数显示 -->
      <view slot="label" class="nickname-input-label"> {{ nicknameCharCount }}/15 </view>
    </view>

    <!-- 弹窗底部按钮区域：取消和保存 -->
    <view class="popup-buttons">
      <text class="popup-button-cancel" bindtap="onCancelNickname">取消</text>
      <text class="popup-button-save" bindtap="onSaveNickname">保存修改</text>
    </view>
  </view>
</t-popup>