/* 选择头像页面样式 - 用户头像选择和更新页面样式 */
.chooseAvatar-my-page {
  margin: 0 40rpx;

  /* 头像选择区域样式 */
  .chooseAvatar {
    padding: 32rpx;
    
    /* 头像列表样式 - 网格布局，自动换行 */
    .avatar-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-top: 98rpx;
    }
    
    /* 头像项样式 - 圆形头像，白色边框 */
    .avatar-item {
      width: 134rpx;
      box-sizing: border-box;
      height: 134rpx;
      border: 6rpx solid #fff;
      border-radius: 50%;
      margin-bottom: 40rpx;
      
      /* 选中状态样式 - 橙色边框高亮 */
      &.avatar-item-selected {
        border-radius: 0rpx 0rpx 0rpx 0rpx;
        border: 6rpx solid #FF9F5B;
        border-radius: 50%;
      }
    }
    
    /* 头像图片样式 - 继承父元素的圆角 */
    .chooseAvatar-icon {
      width: 100%;
      height: 100%;
      border-radius: inherit;
    }
  }
  
  /* 确认按钮区域样式 - 固定定位在底部 */
  .submit {
    position: fixed;
    left: 40rpx;
    right: 40rpx;
    bottom: 84rpx;
    
    /* 确认按钮样式 - 圆角按钮设计 */
    .submit-btn {
      width: 100%;
      display: inline-block;
      height: 112rpx;
      line-height: 112rpx;
      text-align: center;
      font-size: 32rpx;
      color: #fff;
      background: #568ded;
      border-radius: 120rpx;
    }
  }
}
