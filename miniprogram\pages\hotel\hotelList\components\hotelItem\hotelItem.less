/* pages/hotel/hotelList/components/hotelItem/hotelItem.wxss */

.hotel-item {
  display: flex;
  background-color: #fff;
  border-radius: 40rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  padding: 8rpx;
}

/* 左侧图片区域 */
.hotel-image-wrapper {
  width: 232rpx;
  flex-shrink: 0;
  border-radius:32rpx ;
  overflow: hidden;
}

.hotel-image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 右侧信息区域 */
.hotel-info {
  flex: 1;
  padding: 8rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  min-height: 200rpx;
}

/* 酒店名称 */
.hotel-name {
  font-weight: 500;
  font-size: 32rpx;
  color: #11111E;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 评分和星级行 */
.rating-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.rating-score {
  background: #568DED;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #FFFFFF;
  padding: 0 8rpx;
  margin-right: 12rpx;
}

.stars {
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 地区信息 */
.location-info {
  font-size: 24rpx;
  color: #11111E;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.business-zone-name {
  flex:1;
  margin-left: 32rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 酒店标签 */
.features-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  height:44rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.feature-tag {
  height: 44rpx;
  background: #F3F3F3;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: #000000;
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

/* 价格信息 */
.price-info {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
  color: #568DED;
  margin-top: auto;
}

.currency-symbol {
  font-size: 20rpx;
}

.price {
  font-size: 44rpx;
  font-weight: 500;
  margin: 0 4rpx;
}

.price-unit {
  font-size: 20rpx;
}