/* 关于我们页面样式 */
.aboutUs-my-page {
  margin: 16rpx;
  /* 设置功能菜单样式 - 圆角卡片设计 */
  .aboutUs-menu {
    margin: 16rpx;
    border-radius: 40rpx;
    background-color: #fff;

    /* 菜单项样式 - 水平布局，左右对齐 */
    .aboutUs-item {
      display: flex;
      justify-items: space-between;
      align-items: center;
      padding: 0 32rpx;
      margin-bottom: 8rpx;
      height: 104rpx;

      /* 菜单标题样式 - 占据剩余空间 */
      .aboutUs-title {
        flex: 1;
        font-size: 28rpx;
        color: #000;
      }

      /* 箭头图标样式 - 右侧指示箭头 */
      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }
}
