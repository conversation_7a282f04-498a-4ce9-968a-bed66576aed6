import { request } from "../utils/request";
import { uploadImage } from "../utils/upload";

/**
 * 获取用户信息接口
 * 用于获取当前登录用户的基本信息，包括头像、昵称等
 * 
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回用户信息
 */
export const getUserInfo = () => request<My.IUserInfoRes>({
  method: 'GET',
  url: '/hotel/user/profile/userInfo'
})


/**
 * 更新用户信息接口
 * 用于更新当前登录用户的基本信息，包括头像、昵称等
 * 
 * @param data 要更新的用户信息
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回更新后的用户信息
 */
export const updateUserInfo = (data: My.IUpdateUserInfoReq) => request<My.IUserInfoRes>({
  method: 'POST',
  url: '/hotel/user/profile/update',
  data
})



/**
 * 获取基本头像
 * 
 */
export const getAvatar = () => request<string[]>({
  method: 'GET',
  url: '/hotel/user/profile/avatar'
})



/**
 * 发送注销账号短信
 * 
 */
export const sendLogoutCode = () => request({
  method: 'POST',
  url: '/hotel/user/auth/logout/code'
})

/**
 * 账号注销提交
 *
 */
export const sendLogout = (data: My.IFetchLogoutReq) => request({
  method: 'POST',
  url: '/hotel/user/auth/account/logout',
  data
})

/**
 * 上传反馈图片（单张）
 */
export const uploadFeedbackImage = (filePath: string) => {
  console.log('=== 调用上传反馈图片接口 ===');
  console.log('文件路径:', filePath);
  console.log('接口URL:', '/hotel/user/upload/images');
  console.log('参数名:', 'files');

  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: 'files'
  });
}

/**
 * 测试：尝试不同的参数名上传
 */
export const uploadFeedbackImageWithDifferentName = (filePath: string, paramName: string = 'file') => {
  console.log('=== 测试不同参数名上传 ===');
  console.log('文件路径:', filePath);
  console.log('参数名:', paramName);

  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: paramName
  });
}

/**
 * 一次上传多张反馈图片（Base64方式）
 */
export const uploadMultipleFeedbackImagesBase64 = (filePaths: string[]) => {
  console.log('=== 调用多文件上传接口（Base64）===');
  console.log('文件数量:', filePaths.length);
  console.log('文件路径:', filePaths);

  const { uploadMultipleFilesAsBase64 } = require('../utils/upload');
  return uploadMultipleFilesAsBase64(filePaths, {
    url: '/hotel/user/upload/images'
  });
}

/**
 * 一次上传多张反馈图片（FormData方式）
 */
export const uploadMultipleFeedbackImagesFormData = (filePaths: string[]) => {
  console.log('=== 调用多文件上传接口（FormData）===');
  console.log('文件数量:', filePaths.length);
  console.log('文件路径:', filePaths);

  const { uploadMultipleFilesAsFormData } = require('../utils/upload');
  return uploadMultipleFilesAsFormData(filePaths, {
    url: '/hotel/user/upload/images'
  });
}

/**
 * 提交用户反馈
 *
 */
export const submitFeedback = (data: My.ISubmitFeedbackReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/user/feedback/submit',
  data
})
