import { request } from "../utils/request";

/**
 * 获取用户信息接口
 * 用于获取当前登录用户的基本信息，包括头像、昵称等
 * 
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回用户信息
 */
export const getUserInfo = () => request<My.IUserInfoRes>({
  method: 'GET',
  url: '/hotel/user/profile/userInfo'
})


/**
 * 更新用户信息接口
 * 用于更新当前登录用户的基本信息，包括头像、昵称等
 * 
 * @param data 要更新的用户信息
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回更新后的用户信息
 */
export const updateUserInfo = (data: My.IUpdateUserInfoReq) => request<My.IUserInfoRes>({
  method: 'POST',
  url: '/hotel/user/profile/update',
  data
})



/**
 * 获取基本头像
 * 
 */
export const getAvatar = () => request<string[]>({
  method: 'GET',
  url: '/hotel/user/profile/avatar'
})



/**
 * 发送注销账号短信
 * 
 */
export const sendLogoutCode = () => request({
  method: 'POST',
  url: '/hotel/user/auth/logout/code'
})

/**
 * 账号注销提交
 * 
 */
export const sendLogout = (data: My.IFetchLogoutReq) => request({
  method: 'POST',
  url: '/hotel/user/auth/account/logout',
  data
})
