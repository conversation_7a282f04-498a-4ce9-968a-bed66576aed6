import { request } from "../utils/request";
import { uploadImage } from "../utils/upload";

/**
 * 获取用户信息接口
 * 用于获取当前登录用户的基本信息，包括头像、昵称等
 * 
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回用户信息
 */
export const getUserInfo = () => request<My.IUserInfoRes>({
  method: 'GET',
  url: '/hotel/user/profile/userInfo'
})


/**
 * 更新用户信息接口
 * 用于更新当前登录用户的基本信息，包括头像、昵称等
 * 
 * @param data 要更新的用户信息
 * @returns Promise<Request.IResponseResult<My.IUserInfoRes>> 返回更新后的用户信息
 */
export const updateUserInfo = (data: My.IUpdateUserInfoReq) => request<My.IUserInfoRes>({
  method: 'POST',
  url: '/hotel/user/profile/update',
  data
})



/**
 * 获取基本头像
 * 
 */
export const getAvatar = () => request<string[]>({
  method: 'GET',
  url: '/hotel/user/profile/avatar'
})



/**
 * 发送注销账号短信
 * 
 */
export const sendLogoutCode = () => request({
  method: 'POST',
  url: '/hotel/user/auth/logout/code'
})

/**
 * 账号注销提交
 *
 */
export const sendLogout = (data: My.IFetchLogoutReq) => request({
  method: 'POST',
  url: '/hotel/user/auth/account/logout',
  data
})

/**
 * 上传反馈图片（单张）
 */
export const uploadFeedbackImage = (filePath: string) => {
  return uploadImage(filePath, {
    url: '/hotel/user/upload/images',
    name: 'files'
  });
}

/**
 * 一次上传多张反馈图片（FormData方式）
 */
export const uploadMultipleFeedbackImagesFormData = (filePaths: string[]) => {
  const { uploadMultipleFilesAsFormData } = require('../utils/upload');
  return uploadMultipleFilesAsFormData(filePaths, {
    url: '/hotel/user/upload/images'
  });
}



/**
 * 提交用户反馈
 *
 */
export const submitFeedback = (data: My.ISubmitFeedbackReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/user/feedback/submit',
  data
})


/**
 * 获取行为接口
 *
 */
export const getFavorites = (data: My.ISubmitFeedbackReq) => request<My.ISubmitFeedbackRes>({
  method: 'GET',
  url: '/hotel/user/favorites/list',
  data
})

/**
 * 取消收藏接口
 *
 */
export const sendFavorites = (data: My.ISubmitFeedbackReq) => request<My.ISubmitFeedbackRes>({
  method: 'POST',
  url: '/hotel/user/favorites/batch',
  data
})