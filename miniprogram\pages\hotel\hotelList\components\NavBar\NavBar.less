.navbar-container {
  position: relative;
  width: 100%;
  background-color: #ffffff;
}

.navbar-placeholder {
  width: 100%;
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}

/* 左侧返回按钮区域 */
.navbar-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 88rpx;
  padding-left: 20rpx;
}

.navbar-back-icon {
  width: 36rpx;
  height: 36rpx;
  /* 预留图标位置，用户会提供返回图标 */
}

/* 中间城市选择区域 */
.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-city-wrapper {
  display: flex;
  align-items: center;
}

.navbar-city-name {
  font-size: 30rpx;
  max-width: 320rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.navbar-city-arrow {
  width: 0;
  height: 0;
  margin-left: 8rpx;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #333;
  display: inline-block;
  vertical-align: middle;
}