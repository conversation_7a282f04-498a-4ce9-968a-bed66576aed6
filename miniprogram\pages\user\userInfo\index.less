/* 个人资料页面样式 - 用户信息编辑和管理页面样式 */
.userInfo-my-page {
  /* 用户信息编辑区域样式 - 圆角卡片设计 */
  .userInfo {
    margin: 16rpx;
    border-radius: 40rpx;
    background-color: #fff;

    /* 信息项样式 - 水平布局，左右对齐 */
    .userInfo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 32rpx;
      margin-bottom: 8rpx;
      height: 104rpx;

      /* 头像项特殊样式 - 增加高度以容纳头像 */
      &.avatar {
        height: 182rpx;

        /* 头像图片样式 - 圆形头像 */
        .userInfo-avatar {
          width: 118rpx;
          height: 118rpx;
        }
      }

      /* 信息内容区域样式 - 右对齐布局 */
      .userInfo-content {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      /* 信息标题样式 */
      .userInfo-title {
        font-size: 28rpx;
        color: #000;
      }

      /* 信息文本样式 */
      .userInfo-text {
        font-size: 28rpx;
        color: #000;

        /* 空值状态样式 - 灰色文字 */
        &.userInfo-text-empty {
          color: #ccccce;
        }
      }

      /* 箭头图标样式 - 右侧指示箭头 */
      .arrow-icon {
        margin-left: 16rpx;
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  /* 退出登录按钮样式 - 圆角按钮设计 */
  .login-out {
    margin: 0 16rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    background: #ffffff;
    border-radius: 120rpx;
  }
}

/* 昵称修改弹窗内容样式 - 底部弹出层 */
.nickname-popup-content {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 弹窗头部样式 - 居中标题 */
.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 40rpx;
}

/* 弹窗标题样式 */
.popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

/* 关闭按钮样式 - 右上角定位 */
.close-icon {
  position: absolute;
  right: 0;
  font-size: 32rpx;
  color: #999;
  padding: 10rpx;
}

/* 昵称输入框样式 - 圆角输入框 */
.nickname-input {
  height: 108rpx;
  border-radius: 220rpx;
  background: #F9F9F9;
  padding-left: 32rpx;
  font-size: 32rpx;
}

/* 昵称输入容器样式 - 相对定位，用于字符计数 */
.nickname-input-container {
  position: relative;
  margin-bottom: 48rpx;
}

/* 字符计数标签样式 - 右上角定位 */
.nickname-input-label{
  position: absolute;
  right: 32rpx;
  color: #CCCCCE;
  font-size: 26rpx;
  top: 35%; 
}

/* 昵称弹窗按钮区域样式 */
.nickname-popup {
  /* 按钮容器样式 - 水平等分布局 */
  .popup-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  /* 取消按钮样式 - 灰色背景 */
  .popup-button-cancel {
    flex: 1;
    height: 104rpx;
    line-height: 104rpx;
    display: inline-block;
    text-align: center;
    background: #f7f7f7;
    color: #11111e;
    border-radius: 120rpx;
    margin-right: 16rpx;
  }
  
  /* 保存按钮样式 - 蓝色背景 */
  .popup-button-save {
    height: 104rpx;
    line-height: 104rpx;
    text-align: center;
    flex: 1;
    display: inline-block;
    background: #568ded;
    color: #fff;
    border-radius: 120rpx;
  }
}
