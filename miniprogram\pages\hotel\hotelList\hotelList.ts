// pages/hotelList/hotelList.ts
import { getHotelList } from '../../../api/hotel/hotelList';
import { StorageKeyEnum } from '../../../enum/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cityName: '杭州市临平区人民政府',
    cityId: '1201',
    /** 入住时间 */
    arrivalDate: '2025-06-16',
    /** 离店时间 */
    departureDate: '2025-06-17',
    queryText: '上海迪士尼乐园',
    pageIndex:1,
    hotels: [] as Hotel.IHotelList[],
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(option: any) {
    console.log('onLoad option', option);
    Object.keys(option).forEach((key) => {
      this.setData({
        [key]: option[key]
      })
    });
    // 可以从缓存或全局数据中获取城市信息
    wx.setStorageSync(StorageKeyEnum.Token, 'bEzGonA8Xm3pMSwX7A3RmLrd8EFAIk22TaqkTGnudkbkxB63TfjvdPQ5DxPtqikfptOvKGff0u4VLVeWDnZfd6oNEM71qp0R8ZPTEGr0I45b4F4j9Yph7xBAK75amPww');
    this.getHotelList();
  },

  async getHotelList() {
    const { queryText, cityId, arrivalDate, departureDate } = this.data;
    const params = {
      queryText,
      cityId,
      arrivalDate,
      departureDate
    }
    console.log('getHotelList params', params);
    const { code,data: { hasMore, hotels } } = await getHotelList(params);
    if (code === 200) {
      this.setData({
        hotels,
        hasMore
      })
      console.log('getHotelList data', hotels);
    }
  },

  /**
   * 处理酒店卡片点击事件
   */
  handleHotelClick(e: any) {
    const { hotelData } = e.detail;
    console.log('点击酒店：', hotelData);
    
    // 跳转到酒店详情页面
    wx.navigateTo({
      url: `/pages/hotel/detail/detail?hotelId=${hotelData.hotelId}`
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})