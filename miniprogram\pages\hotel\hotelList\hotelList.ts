// pages/hotelList/hotelList.ts
import { hotelListApi, sortTypeApi, priceStarApi, areaApi, filterOptionsApi } from '../../../api/hotel/hotelList';
import { StorageKeyEnum } from '../../../enum/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    cityName: '杭州市临平区人民政府',
    cityId: '1201',
    /** 入住时间 */
    arrivalDate: '2025-06-16',
    /** 离店时间 */
    departureDate: '2025-06-17',
    queryText: '上海迪士尼乐园',
    pageIndex: 1,
    pageSize: 10,
    hotels: [] as Hotel.IHotelList[],
    hasMore: true,
    /** 初始加载状态 */
    loading: false,
    /** 下拉刷新状态 */
    refreshing: false,
    /** 上拉加载更多状态 */
    loadingMore: false,
    /** scroll-view下拉刷新状态 */
    refresherTriggered: false,
    /** 排序列表 */
    sortList:[] as Hotel.ISortType[],
    sort:null as string | null,

    /** 排序类型 */
    typeId:null,
    /** 区域 */
    areaDistance:[] as Hotel.IFilter[],
    /** 筛选选项 */
    filterOptions:[] as Hotel.IFilter[],
    filters:[] as Hotel.IFilter[],
    
    // Filter组件数据
    filterData: {
      currentSortLabel: '智能排序',
      priceStarCount: 3,
      distanceCount: 0,
      filterCount: 3
    },
    // 弹窗控制
    expandPopupVisible: false,
    currentFilterType: '' // 当前活跃的筛选类型: 'sort' | 'priceStar' | 'distance' | 'filter'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(option: any) {
    console.log('onLoad option', option);
    Object.keys(option).forEach((key) => {
      this.setData({
        [key]: option[key]
      })
    });
    // 可以从缓存或全局数据中获取城市信息
    wx.setStorageSync(StorageKeyEnum.Token, 'Ic0oV43XFOkUcA3g0z4dBklk5HTSA0dYuun5JsjOxPBwRtIBgMnfSdXz0XagGyhpmROAWRhqaioOhkBzsFLLxoULxyoSorfj8vnrJGObNX9cKsgQ9y14gjQ3E9pcWfHj');
     this.getPriceStar();
     this.getAreaDistance();
     this.getFilterOptions();
     await this.getSortType();
     this.getHotelList();
  },

  async getHotelList(isLoadMore = false) {
    const { queryText, cityId, arrivalDate, departureDate, pageIndex, pageSize, filters, sort } = this.data;
    const params = {
      queryText,
      cityId,
      arrivalDate,
      departureDate,
      sort,
      pageIndex,
      pageSize,
      filters
    }
    
    console.log('getHotelList params', params);
    
    try {
      // 设置加载状态
      if (isLoadMore) {
        this.setData({ loadingMore: true });
      } else if (pageIndex === 1) {
        this.setData({ loading: true });
      }
      
      const { code, data: { hasMore, hotels } } = await hotelListApi(params);
      
      if (code === 200) {
        const newData: any = { hasMore };
        
        if (isLoadMore) {
          // 上拉加载更多，追加数据
          newData.hotels = [...this.data.hotels, ...hotels];
          newData.loadingMore = false;
        } else {
          // 初始加载或下拉刷新，替换数据
          newData.hotels = hotels;
          newData.loading = false;
          newData.refreshing = false;
          newData.refresherTriggered = false;
        }
        
        this.setData(newData);
      }
    } catch (error) {
      console.error('getHotelList error', error);
      // 重置加载状态
      this.setData({
        loading: false,
        refreshing: false,
        loadingMore: false,
        refresherTriggered: false
      });
      
      // 显示错误提示
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 处理酒店卡片点击事件
   */
  handleHotelClick(e: any) {
    const { hotelData } = e.detail;
    console.log('点击酒店：', hotelData);
    
    // 跳转到酒店详情页面
    wx.navigateTo({
      url: `/pages/hotel/detail/detail?hotelId=${hotelData.hotelId}`
    });
  },

  /**
   * 处理搜索事件
   */
  handleSearch(e: any) {
    console.log('搜索事件触发', e.detail);
    // 重置分页状态，重新加载数据
    this.setData({
      pageIndex: 1,
      hotels: []
    });
    this.getHotelList(false);
  },


  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },


  /**
   * 处理展开弹窗关闭事件
   */
  onExpandPopupClose() {
    this.setData({
      expandPopupVisible: false,
      currentFilterType: ''
    });
  },

  /**
   * 筛选组件事件处理 ===================================================================
   */
  
  // 处理排序选择
  onSortSelect(e: any) {
    console.log('排序选择:', e.detail);
    const { sortOption } = e.detail;
    
    // 更新当前排序显示
    this.setData({
      'filterData.currentSortLabel': sortOption.label,
      typeId: sortOption.value
    });
    
    // 关闭弹窗并重新加载数据
    this.onExpandPopupClose();
    this.setData({ pageIndex: 1, hotels: [] });
    this.getHotelList(false);
  },

  // 处理价格/星级变更
  onPriceStarChange(e: any) {
    console.log('价格/星级变更:', e.detail);
    const { selectedCount, selectedOptions } = e.detail;
    
    // 更新价格/星级选中数量
    this.setData({
      'filterData.priceStarCount': selectedCount
    });
    
    // 这里可以处理价格/星级筛选逻辑
  },

  // 处理位置距离变更
  onDistanceChange(e: any) {
    console.log('位置距离变更:', e.detail);
    const { selectedCount, selectedOptions } = e.detail;
    
    // 更新位置距离选中数量
    this.setData({
      'filterData.distanceCount': selectedCount
    });
    
    // 这里可以处理位置距离筛选逻辑
  },

  // 处理更多筛选变更
  onFilterChange(e: any) {
    console.log('更多筛选变更:', e.detail);
    const { selectedCount, selectedOptions } = e.detail;
    
    // 更新筛选选中数量
    this.setData({
      'filterData.filterCount': selectedCount
    });
    
    // 这里可以处理更多筛选逻辑
  },

  /**
   * scroll-view下拉刷新事件处理---------------------------------------------------------------------------------------------
   */
  async onRefresherRefresh() {
    this.setData({
      refreshing: true,
      refresherTriggered: true,
      pageIndex: 1
    });
    
    await this.getHotelList(false);
  },

  /**
   * scroll-view滚动到底部事件处理
   */
  async onScrollToLower() {
    const { hasMore, loadingMore } = this.data;
    // 如果没有更多数据或正在加载中，则不执行
    if (!hasMore || loadingMore) {
      return;
    }
    
    // 增加页码
    const newPageIndex = this.data.pageIndex + 1;
    this.setData({ pageIndex: newPageIndex });
    
    // 加载更多数据
    await this.getHotelList(true);
  },

  /**
   * Filter组件事件处理 ===================================================================
   */
  
  // 处理排序点击事件
  onSortClick(e: any) {
    console.log('排序点击:', e.detail);
    
    if (this.data.currentFilterType === 'sort' && this.data.expandPopupVisible) {
      // 当前已展开排序，点击收起
      this.setData({
        expandPopupVisible: false,
        currentFilterType: ''
      });
    } else {
      // 展开排序或切换到排序
      this.setData({
        currentFilterType: 'sort',
        expandPopupVisible: true
      });
    }
  },

  // 处理价格/星级点击事件  
  onPriceStarClick(e: any) {
    console.log('价格/星级点击:', e.detail);
    
    if (this.data.currentFilterType === 'priceStar' && this.data.expandPopupVisible) {
      // 当前已展开价格/星级，点击收起
      this.setData({
        expandPopupVisible: false,
        currentFilterType: ''
      });
    } else {
      // 展开价格/星级或切换到价格/星级
      this.setData({
        currentFilterType: 'priceStar',
        expandPopupVisible: true
      });
    }
  },

  // 处理位置距离点击事件
  onDistanceClick(e: any) {
    console.log('位置距离点击:', e.detail);
    
    if (this.data.currentFilterType === 'distance' && this.data.expandPopupVisible) {
      // 当前已展开位置距离，点击收起
      this.setData({
        expandPopupVisible: false,
        currentFilterType: ''
      });
    } else {
      // 展开位置距离或切换到位置距离
      this.setData({
        currentFilterType: 'distance',
        expandPopupVisible: true
      });
    }
  },

  // 处理筛选点击事件
  onFilterClick(e: any) {
    console.log('筛选点击:', e.detail);
    
    if (this.data.currentFilterType === 'filter' && this.data.expandPopupVisible) {
      // 当前已展开筛选，点击收起
      this.setData({
        expandPopupVisible: false,
        currentFilterType: ''
      });
    } else {
      // 展开筛选或切换到筛选
      this.setData({
        currentFilterType: 'filter',
        expandPopupVisible: true
      });
    }
  },

  // 获取排序方式---------------------------------------------------------------------------------------------
  async getSortType() {
    const { code, data } = await sortTypeApi();
    if (code === 200) {
      this.setData({ 
        sortList: data.sort,
        sort: data.sort[0].key,
        "filterData.currentSortLabel": data.sort[0].desc,
      });
    }
  },
  // 获取价格星级
  async getPriceStar() {
    const { code, data } = await priceStarApi();
    if (code === 200) {
      this.setData({ 
        priceRangeVOs: data.priceRangeVOs,
        starRatingVOs: data.starRatingVOs
      });
    }
  },
  // 获取区域
  async getAreaDistance() {
    let params = {
      cityId: this.data.cityId,
      typeId: this.data.typeId
    }
    const { code, data } = await areaApi( params );
    if (code === 200) {
      this.setData({ areaDistance: data });
    }
  },
  // 获取筛选选项
  async getFilterOptions() {
    const { code, data } = await filterOptionsApi( this.data.cityId );
    if (code === 200) {
      this.setData({ filterOptions: data });
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
   /**
   * 生命周期函数--监听页面隐藏
   */
   onHide() {

   },
 
   /**
    * 生命周期函数--监听页面卸载
    */
   onUnload() {
 
   },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 已改为scroll-view实现，此方法保留但不使用
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 已改为scroll-view实现，此方法保留但不使用
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }




})