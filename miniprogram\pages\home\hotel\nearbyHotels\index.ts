import type { TMapInstance } from "../../../../components/tencentMap/types";
import { ComponentWithComputedStore } from "../../../../core/componentWithStoreComputed";
import { storeBindingsBehavior } from "mobx-miniprogram-bindings";
import { store } from "../../../../store/index";

ComponentWithComputedStore({
  behaviors: [storeBindingsBehavior],
  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [] as Home.IRecommendHotel[]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  storeBindings: {
    store,
    fields: ['geoInfo'],
    actions: {}
  } as const,
  customInstanceProperty: {
    compInstance: null as Nullable<TMapInstance>
  },

  lifetimes: {
    attached() {
      this.compInstance = this.selectComponent('.nearby-hotel-map') as TMapInstance;
    }
  },

  observers: {
    list() {
      if (this.data.list && this.compInstance) {
        this.compInstance.setMarkers({
          markers: this.data.list.map(item => ({
            id: Number(item.outId),
            longitude: item.longitude,
            latitude: item.latitude,
            price: item.minPrice,
            currency: item.currency
          }))
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleCalloutTap(e: WechatMiniprogram.CustomEvent<{markerId: number}>) {
      this.compInstance?.setMarkersActive({
        markerIds: [e.detail.markerId]
      });
    }
  }
})