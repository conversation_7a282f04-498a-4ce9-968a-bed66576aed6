// pages/user/collection/index.ts
import { getFavorites, sendFavorites } from "../../../api/my";
import { getImageUrl } from "../../../utils/images";

Page({
  data: {
    // 工具函数
    getImageUrl,

    // Tab相关
    currentTab: 'favorite',
    tabs: [
      { label: '收藏', value: 'favorite' },
      { label: '看过', value: 'viewed' },
      { label: '住过', value: 'stayed' },
      { label: '赞过', value: 'liked' }
    ],
    deleteIcon: getImageUrl('user/delete_red.png'),
    circleIcon: getImageUrl('user/circle_black.png'),
    circleActIcon: getImageUrl('user/circle_act.png'),
    // 编辑模式
    isEditMode: false,
    selectedItems: [] as string[], // 选中的项目ID列表
    isAllSelected: false,
    
    // 数据列表
    favoriteList: [] as any[],
    viewedList: [] as any[],
    stayedList: [] as any[],
    likedList: [] as any[],
    
    // 加载状态
    loading: false,
    
    // 右滑相关
    touchStartX: 0,
    touchStartY: 0,
    currentSwipeIndex: -1, // 当前右滑的项目索引
  },

  onLoad() {
    // 初始化加载收藏数据
    this.loadFavorites();
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 加载收藏数据
   */
  async loadFavorites() {
    this.setData({ loading: true });

    try {
      const { isSuccess, data } = await getFavorites({ type: 'favorite' });
      if (isSuccess && data) {
        this.setData({ favoriteList: data });
      } else {
        // 如果接口失败，使用模拟数据
        const mockData = [
          {
            id: '1',
            name: '全季古北财富中心酒店',
            type: '舒适型',
            rating: 4.8,
            location: '虹桥地区',
            subway: '靠近伊犁路(地铁站)',
            amenities: ['自助洗衣', '免费停车', '机器人服务', '健身房'],
            price: 695,
            image: 'https://example.com/hotel1.jpg'
          },
          {
            id: '2',
            name: '全季上海虹桥古北财富中心',
            type: '舒适型',
            rating: 4.8,
            location: '虹桥地区',
            subway: '靠近伊犁路(地铁站)',
            amenities: ['自助洗衣', '免费停车', '机器人服务', '健身房'],
            price: 695,
            image: 'https://example.com/hotel2.jpg'
          }
        ];
        this.setData({ favoriteList: mockData });
      }

      this.setData({ loading: false });
    } catch (error) {
      console.error('加载收藏数据失败:', error);
      this.setData({ loading: false });

      // 错误时显示提示
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * Tab切换
   */
  onTabChange(e: any) {
    const { value } = e.detail;
    this.setData({
      currentTab: value,
      isEditMode: false,
      selectedItems: [],
      currentSwipeIndex: -1
    });

    // 根据tab加载对应数据
    switch (value) {
      case 'favorite':
        this.loadFavorites();
        break;
      case 'viewed':
        // TODO: 加载看过数据
        break;
      case 'stayed':
        // TODO: 加载住过数据
        break;
      case 'liked':
        // TODO: 加载赞过数据
        break;
    }
  },

  /**
   * 切换编辑模式
   */
  onToggleEditMode() {
    const isEditMode = !this.data.isEditMode;
    this.setData({
      isEditMode,
      selectedItems: [],
      isAllSelected: false,
      currentSwipeIndex: -1
    });
  },

  /**
   * 选择/取消选择项目
   */
  onSelectItem(e: any) {
    if (!this.data.isEditMode) return;
    
    const itemId = e.currentTarget.dataset.id;
    const selectedItems = [...this.data.selectedItems];
    const index = selectedItems.indexOf(itemId);
    
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }
    
    const currentList = this.getCurrentList();
    const isAllSelected = selectedItems.length === currentList.length;
    
    this.setData({
      selectedItems,
      isAllSelected
    });
  },

  /**
   * 全选/取消全选
   */
  onToggleSelectAll() {
    const currentList = this.getCurrentList();
    const isAllSelected = !this.data.isAllSelected;
    
    this.setData({
      isAllSelected,
      selectedItems: isAllSelected ? currentList.map(item => item.id) : []
    });
  },

  /**
   * 获取当前Tab的数据列表
   */
  getCurrentList() {
    const { currentTab, favoriteList, viewedList, stayedList, likedList } = this.data;
    switch (currentTab) {
      case 'favorite': return favoriteList;
      case 'viewed': return viewedList;
      case 'stayed': return stayedList;
      case 'liked': return likedList;
      default: return [];
    }
  },

  /**
   * 触摸开始
   */
  onTouchStart(e: any) {
    if (this.data.isEditMode) return;
    
    const touch = e.touches[0];
    this.setData({
      touchStartX: touch.clientX,
      touchStartY: touch.clientY
    });
  },

  /**
   * 触摸移动
   */
  onTouchMove(e: any) {
    if (this.data.isEditMode) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - this.data.touchStartX;
    const deltaY = touch.clientY - this.data.touchStartY;
    
    // 判断是否为水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
      const index = e.currentTarget.dataset.index;
      
      if (deltaX < -50) {
        // 向左滑动，显示删除按钮
        this.setData({ currentSwipeIndex: index });
      } else if (deltaX > 50) {
        // 向右滑动，隐藏删除按钮
        this.setData({ currentSwipeIndex: -1 });
      }
    }
  },

  /**
   * 触摸结束
   */
  onTouchEnd() {
    // 重置触摸状态
    this.setData({
      touchStartX: 0,
      touchStartY: 0
    });
  },

  /**
   * 删除单个项目
   */
  async onDeleteItem(e: any) {
    const itemId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个收藏吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            // TODO: 调用删除接口
            // await deleteFavorite(itemId);
            
            // 从列表中移除
            const currentList = this.getCurrentList();
            const newList = currentList.filter(item => item.id !== itemId);
            this.updateCurrentList(newList);
            
            this.setData({ currentSwipeIndex: -1 });
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
          } catch (error) {
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 批量取消收藏
   */
  async onBatchCancel() {
    if (this.data.selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要取消的项目',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认取消',
      content: `确定要取消收藏这${this.data.selectedItems.length}个项目吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const { isSuccess } = await sendFavorites({
              ids: this.data.selectedItems,
              action: 'cancel'
            });

            if (isSuccess) {
              // 从列表中移除选中项目
              const currentList = this.getCurrentList();
              const newList = currentList.filter(item => !this.data.selectedItems.includes(item.id));
              this.updateCurrentList(newList);

              this.setData({
                selectedItems: [],
                isAllSelected: false,
                isEditMode: false
              });

              wx.showToast({
                title: '取消成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '操作失败',
                icon: 'none'
              });
            }
          } catch (error) {
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 更新当前Tab的数据列表
   */
  updateCurrentList(newList: any[]) {
    const { currentTab } = this.data;
    switch (currentTab) {
      case 'favorite':
        this.setData({ favoriteList: newList });
        break;
      case 'viewed':
        this.setData({ viewedList: newList });
        break;
      case 'stayed':
        this.setData({ stayedList: newList });
        break;
      case 'liked':
        this.setData({ likedList: newList });
        break;
    }
  },

  /**
   * 点击项目（非编辑模式下跳转详情）
   */
  onItemClick(e: any) {
    if (this.data.isEditMode) {
      this.onSelectItem(e);
    } else {
      const itemId = e.currentTarget.dataset.id;
      // TODO: 跳转到详情页
      console.log('跳转到详情页:', itemId);
    }
  }
});
