{"name": "miniprogram-api-typings", "version": "4.0.7", "description": "Type definitions for APIs of Wechat Mini Program in TypeScript", "main": "./index.d.ts", "types": "./index.d.ts", "scripts": {"test": "npm run tsd && npm run eslint", "tsd": "tsd", "eslint": "eslint --config .eslintrc.js types/**/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/wechat-miniprogram/api-typings.git"}, "author": "Wechat Miniprogram <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wechat-miniprogram/api-typings/issues"}, "homepage": "https://github.com/wechat-miniprogram/api-typings#readme", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.46.0", "@typescript-eslint/parser": "^5.46.0", "eslint": "^8.29.0", "tsd": "^0.25.0", "typescript": "^4.9.4"}, "tsd": {"directory": "test"}, "files": ["LICENSE", "CHANGELOG.md", "README.md", "README-en.md", "index.d.ts", "typings.json", "types/"]}