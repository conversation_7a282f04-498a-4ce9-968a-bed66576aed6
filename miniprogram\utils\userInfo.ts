import { getOpenIdOrUnionId } from "../api/user";
import { APIResponseCode } from '../enum/index';
import { StorageKeyEnum } from '../enum/index';

/** 登录过期 */
export const isLoginExpired = (code: number) => [APIResponseCode.UNAUTHORIZED].includes(code);

/**
 * 检查本地存储中是否存在 token
 * @returns boolean 是否存在 token
 */
export const hasToken = (): boolean => {
  const token = wx.getStorageSync(StorageKeyEnum.Token);
  return !!token;
};

/** 获取 loginCode */
export const getLoginCode = () => {
  return new Promise<string | null>((resolve) => {
    wx.login({
      success(e) {
        resolve(e.code);
      },
      fail(err) {
        console.error("getLoginCode.fail:\n", err);
        resolve(null);
      },
    });
  });
};

/**
 * 获取openid unionid
 */
export const getOpenId = async () => {
  const result = {
    openid: '',
    unionid: ''
  }
  const code = await getLoginCode();
  if (code) {
    const { isSuccess, data } = await getOpenIdOrUnionId({ loginCode: code });
    if (isSuccess) {
      result.openid = data.openid;
      result.unionid = data.unionid;
    }
  }
  return result;
}