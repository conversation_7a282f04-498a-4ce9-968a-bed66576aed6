<!-- pages/hotel/hotelList/components/hotelItem/hotelItem.wxml -->
<wxs module="hotelItem" src="./HotelItem.wxs"></wxs>
<view class="hotel-item" bindtap="handleHotelClick">
    <!-- 左侧酒店图片 -->
    <view class="hotel-image-wrapper">
        <image class="hotel-image" src="{{hotelData.hotel.thumbNailUrl}}" mode="aspectFill"></image>
    </view>
    <!-- 右侧酒店信息 -->
    <view class="hotel-info">
        <!-- 酒店名称 -->
        <view class="hotel-name" wx:if="{{hotelData.hotel.hotelName}}">
            {{hotelData.hotel.hotelName}}
        </view>
        <!-- 评分和星级 -->
        <view class="rating-row" wx:if="{{hotelData.hotel.review.score > 0 || hotelData.hotel.review.starRate > 0}}">
            <view class="rating-score" wx:if="{{hotelData.hotel.review.score > 0}}">
                {{hotelItem.formatScore(hotelData.hotel.review.score)}}
            </view>
            <view class="stars" wx:if="{{hotelData.hotel.review.starRate > 0}}">
                <image wx:for="{{hotelData.hotel.review.starRate}}" wx:key="index" class="star-icon" src="{{imgBaseUrl}}/hoteList/starIcon.png"></image>
            </view>
        </view>
        <!-- 地区信息 -->
        <view class="location-info" wx:if="{{hotelData.hotel.districtName || hotelData.hotel.businessZoneName}}">
            <view wx:if="{{hotelData.hotel.districtName}}">{{hotelData.hotel.districtName}}</view>
            <view class="business-zone-name" wx:if="{{hotelData.hotel.businessZoneName}}">
                靠近{{hotelData.hotel.businessZoneName}}
            </view>
        </view>
        <!-- 酒店标签 -->
        <view class="features-row" wx:if="{{hotelData.hotel.features && hotelData.hotel.features.length > 0}}">
            <view class="feature-tag" wx:for="{{hotelData.hotel.features}}" wx:key="index" wx:if="{{index < 4}}">
                {{item}}
            </view>
        </view>
        <!-- 价格信息 -->
        <view class="price-info" wx:if="{{hotelData.lowRate}}">
            <text class="currency-symbol">{{hotelData.currencySymbol}}</text>
            <text class="price">{{hotelData.lowRate}}</text>
            <text class="price-unit">起</text>
        </view>
    </view>
</view>