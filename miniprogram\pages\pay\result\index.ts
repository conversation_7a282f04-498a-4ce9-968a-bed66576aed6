import { queryPayStatus } from '../../../api/pay';
import { PayStatusEnum } from '../../../enum/index';
import Toast, { hideToast } from '../../../miniprogram_npm/tdesign-miniprogram/toast/index';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    /** 业务状态 */
    status: null as Nullable<'SUCCESS' | 'FAIL'>,
    /** 订单号 */
    orderNo: '',
    /** 轮询时长 */
    loopTime: 10000,
    /** 轮询开关 */
    loopOnOff: false,
  },

  customInstanceProperty: {
    __timer: null as Nullable<number>,
    __timerRequest: null as Nullable<number>,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options: { orderNo: string }) {
    const { orderNo } = options;
    this.setData({
      orderNo,
      loopOnOff: true,
    });
    Toast({
      context: this,
      selector: '#t-toast',
      message: '查询状态中...',
      theme: 'loading',
      duration: -1,
      direction: 'column',
    });
    this.executeTask();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.handleClear();
    if (this.data.loopOnOff) {
      this.setData({
        loopOnOff: false,
      });
      hideToast({
        context: this,
        selector: '#t-toast',
      });
    }
  },

  handleLaunchApp() {},
  executeTask() {
    const { loopTime } = this.data;
    this.__timer = setTimeout(() => {
      this.setData({
        loopOnOff: false,
      });
      this.handleClear();
    }, loopTime);
    this.queryStatus();
    this.__timerRequest = setInterval(() => {
      if (this.data.loopOnOff) {
        this.queryStatus();
      } else {
        this.handleClear();
      }
    }, 1000);
  },
  async queryStatus() {
    const res = await queryPayStatus(this.data.orderNo);
    if (res.isSuccess) {
      const { recordList = [] } = res.data;
      if (recordList.length) {
        const { payStatus } = recordList[0];
        if (payStatus === PayStatusEnum.Pending) return;
        this.setData({
          status: payStatus === PayStatusEnum.Paid ? 'SUCCESS' : 'FAIL',
          loopOnOff: false,
        });
        this.handleClear();
      }
    }
  },
  handleClear() {
    this.__timer && clearTimeout(this.__timer);
    this.__timerRequest && clearInterval(this.__timerRequest);
    hideToast({
      context: this,
      selector: '#t-toast',
    });
  },
});