<!-- 选择头像页面 - 用户头像选择和更新页面 -->
<view class="chooseAvatar-my-page">
  <!-- 头像选择区域 -->
  <view class="chooseAvatar">
    <!-- 头像列表网格布局 -->
    <view class="avatar-list">
      <!-- 头像项：根据选中状态动态应用样式类，绑定点击事件 -->
      <view 
        class="avatar-item {{item.selected ? 'avatar-item-selected' : ''}}" 
        wx:for="{{avatarList}}" 
        wx:key="url"
        data-index="{{index}}"
        bindtap="onAvatarClick"
      >
        <image class="chooseAvatar-icon" src="{{item.url}}"></image>
      </view>
    </view>
  </view>
  
  <!-- 确认按钮区域：绑定提交事件 -->
  <view class="submit" bindtap="onSubmit">
    <text class="submit-btn">确定</text>
  </view>
</view>