/** reqeust 类型 */
declare namespace Request {
  interface IRequestOption<T extends AnyObject = AnyObject>
    extends Partial<
      Omit<
        WechatMiniprogram.RequestOption<T>,
        "success" | "fail" | "complete" | "data"
      >
    > {
    /** 接口地址 */
    url: string;
    /** 接口根 url */
    baseURL?: string;
    /** 取消请求场景，传入一个对象，会将 RequestTask 实例赋值给这个对象的 task 属性 */
    controller?: { task: WechatMiniprogram.RequestTask | WechatMiniprogram.UploadTask };
    /** 接口传参 */
    data?: T;
    params?: {
      [x: string]: any
    },
    /** 忽略接口异常 */
    ignoreApiError?: boolean;
    /** 忽略登录过期 */
    ignoreLoginExpired?: boolean;
    /** 登录信息失效，跳转至登录页 */
    forceLogin?: boolean;
    /** mock 数据，配置后不会调用接口，将 mockData 作为结果返回 */
    mockData?: any
    /** 请求类型，默认使用 wx.request */
    requestType?: 'request' | 'uploadFile'
    /** 要上传文件资源的路径 (本地路径)	*/
    filePath?: string
    /** 文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容 */
    name?: string
    /** 延迟响应 */
    delayResponse?: number
  }

  interface IResponseResult<
    T extends boolean | string | AnyObject | ArrayBuffer = AnyObject
  > {
    isSuccess?: boolean,
    code: number;
    data: T;
    message: string;
  }
}
