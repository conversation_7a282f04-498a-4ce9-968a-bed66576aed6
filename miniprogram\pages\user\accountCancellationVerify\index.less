/* 账号注销验证页面样式 */
.accountCancellation-verify-page {
  .top {
    margin: 40rpx 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .account-arrow-icon {
      width: 44rpx;
      height: 44rpx;
    }
    .step {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      padding: 28rpx 24rpx;
      border-radius: 48rpx 48rpx 48rpx 48rpx;
      &.act {
        background-color: #568ded;
        .step-content {
          .step-title {
            color: #fff;
          }
          .step-tip {
            color: #fff;
          }
        }
      }
      .step-icon {
        width: 36rpx;
        height: 36rpx;
        background-color: #f7f7f7;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 18rpx;
      }
      .step-content {
        margin-left: 16rpx;
        .step-title {
          display: block;
          color: #000;
          font-size: 24rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
        }
        .step-tip {
          display: block;
          color: #66666e;
          font-size: 24rpx;
        }
      }
    }
  }
  .info-tip {
    margin: 214rpx auto 16rpx;
    text-align: center;
    .info-tip-text {
      color: #fb7a1e;
      font-size: 28rpx;
    }
  }
  .submit {
    margin: 16rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    background: #568ded;
    border-radius: 120rpx;
    .submit-btn {
      color: #fff;
      font-size: 32rpx;
    }
  }
  .verify{
    padding: 32px;
    margin: 48rpx 16rpx 0;
    background-color: #fff;
    border-radius: 48rpx;
    .verify-title{
      margin-bottom: 32rpx;
      font-size: 24rpx;
      color: #33333E;
    }
    .verify-countdown{
      margin-top: 32rpx;
      text-align: right;
      font-size: 32rpx;
      color: #33333E;
    }
    .verify-code {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .code-box {
        width: 104rpx;
        height: 104rpx;
        margin: 0 4rpx;
        border-radius: 32rpx;
        background: #F7F7F7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        position: relative;
        box-sizing: border-box;
        transition: border-color 0.2s;
      }
      .code-cursor {
        width: 2rpx;
        height: 48rpx;
        background: #000;
        position: absolute;
        left: 50%;
        top: 28rpx;
        transform: translateX(-50%);
        animation: blink 1s steps(1) infinite;
      }
      @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
      }
      .code-input {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        z-index: 2;
        border: none;
        background: transparent;
        caret-color: transparent;
      }
    }
  }
}
