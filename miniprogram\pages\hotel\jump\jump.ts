// pages/hotel/jump/jump.ts
Page({

  /**
   * 页面的初始数据
   */
  data: {
    queryText: '',
    cityId: '',
    cityName: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      queryText: options.queryText,
      cityId: options.cityId,
      cityName: options.cityName
    });
  },

  handleInput(e: any) {
    if(e.currentTarget.dataset.type === 'queryText') {
      this.setData({
        queryText: e.detail.value
      });
    } else if(e.currentTarget.dataset.type === 'cityName') {
      this.setData({
        cityName: e.detail.value
      });
    }
  },
  queryTextClick() {
    wx.redirectTo({
      url: '/pages/hotel/hotelList/hotelList?queryText=' + this.data.queryText
    });
  },
  cityClick() {
    wx.redirectTo({
      url: '/pages/hotel/hotelList/hotelList?cityId='+this.data.cityId+'&cityName='+this.data.cityName
    });
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})