declare namespace Pay {
  interface IGetPayParams {
    /** 支付渠道 */
    payType: "WECHAT",
    /** 订单号 */
    orderNo: string;
    /** 订单实付金额 */
    orderPayMoney: number;
    /** 商品名称 */
    goodsName: string;
    openid: string;
  }
  
  interface ITradeInfo {
    /** 流水号 */
    recordNo: string;
    /** 支付金额 */
    paidMoney: number;
    /** 支付时间 */
    payTime: string;
    /** 支付参数集合 */
    payload: string;
    /** 支付状态 */
    payStatus: number;
  }
  
  interface IWxPayTrade<T> {
    /** 订单号 */
    orderNo: string;
    recordList: Array<T>;
  }

  interface IPaymentInfo {
    /** 实付金额 */
    totalActualPrice: string;
    /** 商品名称 */
    hotelName: string;
    /** 付款商户名 */
    companyName: string;
  }
}
