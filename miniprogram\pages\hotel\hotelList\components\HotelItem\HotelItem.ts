// pages/hotel/hotelList/components/hotelItem/hotelItem.ts
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 酒店数据对象
    hotelData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 图片基础URL
    imgBaseUrl: getApp().globalData.imgBaseUrl,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理酒店卡片点击事件
     */
    handleHotelClick() {
      // 触发酒店点击事件，传递酒店数据
      this.triggerEvent('hotelClick', {
        hotelData: this.properties.hotelData
      });
    }
  }
})