export interface IMapData {
	markers: IMarker[]
}

export type IMapProperty = {
	longitude: {
		type: NumberConstructor,
		value: number
	},
	latitude: {
		type: NumberConstructor,
		value: number
	},
	scale: {
		type: NumberConstructor,
		value: number
	},
	minScale: {
		type: NumberConstructor,
		value: number
	},
	maxScale: {
		type: NumberConstructor,
		value: number
	},
	showScale: {
		type: BooleanConstructor,
		value: boolean
	},
	showLocation: {
		type: BooleanConstructor,
		value: boolean
	},
	enableZoom: {
		type: BooleanConstructor,
		value: boolean
	},
	enableScroll: {
		type: BooleanConstructor,
		value: boolean
	},
	enableRotate: {
		type: BooleanConstructor,
		value: boolean
	},
	enablePoi: {
		type: BooleanConstructor,
		value: boolean
	},
	// markers: {
	// 	type: ArrayConstructor,
	// 	value: any[]
	// }
}

interface IMarkerOption extends ILocation {
	/** marker id */
	id: number;
	/** 币种 */
	currency: string;
	/** 价格 */
	price: number;
}

interface ICustomCallout {
	/** 'BYCLICK':点击显示; 'ALWAYS':常显 */
	display: 'ALWAYS' | 'BYCLICK';
	/** 横向偏移量，向右为正数 */
	anchorX: number;
	/** 纵向偏移量，向下为正数 */
	anchorY: number;
}

export type IMarker = Pick<IMarkerOption, 'latitude' | 'longitude' | 'id'> & Partial<{
	callout: Partial<{
		/** 文本对齐方式。有效值: left, right, center */
		textAlign: 'left' | 'right' | 'center';
		/** 文本边缘留白 */
		padding: number;
		/** 背景色 */
		bgColor: string;
		/** 边框颜色 */
		borderColor: string;
		/** 边框宽度 */
		borderWidth: number;
		/** 边框圆角 */
		borderRadius: number;
		/** 文字大小 */
		fontSize: number;
		/** 文本颜色 */
		color: string;
		/** 文本 */
		content: string;

	} & ICustomCallout>;
	customCallout: Partial<ICustomCallout>;
	/** marker点是否处于激活状态 */
	active?: boolean;
	/** 标注图标宽度 */
	width: number;
	/** 标注图标高度 */
	height: number;
	/** 标注的透明度 */
	alpha: number;
	[x: string]: any
}>

export type IMapMethod = {
	setMarkers: (options: Omit<WechatMiniprogram.AddMarkersOption, 'markers'> & {
		markers: IMarkerOption[]
	}) => void;
	handleCalloutTap: (e: WechatMiniprogram.CustomEvent<{ markerId: number }>) => void;
	setMarkersActive: (options: {
		/** 需要激活的markerId集合 */
		markerIds: number[];
		/** 是否先清空地图上所有 marker激活状态 */
		clear?: boolean;
	}) => void
}

export interface IMapCustomProperty {
	__mapVm: Nullable<WechatMiniprogram.MapContext>
}

export type TMapInstance = WechatMiniprogram.Component.Instance<IMapData, IMapProperty, IMapMethod, WechatMiniprogram.Component.BehaviorOption, IMapCustomProperty>