import { isProdEnv, delay } from './index';
import appConfig from '../config/app.config';
import { isLoginExpired } from './userInfo';
import { showToast } from '../utils/wx';
import { StorageKeyEnum, APIResponseCode } from '../enum/index';

const isRequestSuccess = (
  code: number,
): boolean => code === APIResponseCode.SUCCESS;

export const baseRequest = <
  D extends boolean | string | AnyObject | ArrayBuffer
>({
  baseURL = appConfig.baseUrl + '/api',
  method = 'POST',
  controller,
  timeout = appConfig.requestTimeout,
  params = {},
  ...config
}: Request.IRequestOption<any>): Promise<
  WechatMiniprogram.RequestSuccessCallbackResult<Request.IResponseResult<D>>
> => {
  return new Promise((resolve, reject) => {
    if (!config.header) config.header = {};

    const token = wx.getStorageSync(StorageKeyEnum.Token);
 
    if (token) {
      config.header['HT-Token'] = token;
    }

    if (method.toUpperCase() === 'GET' && Object.keys(params).length && !config.url.includes('?')) {
      /**  */
      config.url += `?${Object.entries(params).map((item) => {
        return `${item[0]}=${item[1]}`;
      }).join('&')}`;
    }

    // 接口需求登录，且没有 token，跳转至登录页
    // if (config.forceLogin && !token) {
    //   // TODO: 跳转至登录
    //   return;
    // }

    Object.assign(config.header, appConfig.defaultHeaders);
    const _data = config.data || {};

    const { content, ...restData } = _data;

    if (!isProdEnv) {
      // console.info(`request:\t${config.url}  接口的最终传参: \n`, {
      //   ...restData,
      //   content: content?.length > 100 ? content.slice(0, 100) + '...' : '',
      // });
    }

    if (config.mockData) {
      resolve({
        data: config.mockData,
      } as WechatMiniprogram.RequestSuccessCallbackResult<Request.IResponseResult<D>>);
      // console.info(`request:\t${config.url}  mockData: \n`, config.mockData);
      return;
    }

    const requestTask = wx.request({
      ...config,
      timeout,
      url: `${baseURL}${config.url}`,
      method,
      success: (
        res: WechatMiniprogram.RequestSuccessCallbackResult<
          Request.IResponseResult<D>
        >,
      ) => {
        // 未登录状态，跳转至登录页
        if (isLoginExpired(res.data.code) && !config.ignoreLoginExpired) {
          // TODO: 登出
          // TODO：跳转登录
        }
        // console.info(`request:\t${config.url}  接口的响应内容: \n`, res);
        resolve(res);
      },
      fail: (err) => {
        debugger;
        reject(err);
      },
    });

    if (controller) {
      controller.task = requestTask;
    }
  });
};

/**
 * 做了错误处理的 request，需要原始的 request 可以用 baseRequest
 *
 * 1. 对于 request 报错的调用，toast 提示 error 对象的 message || '请重试'
 * 2. 对于 http 状态码 不为 200 或 code 不为 200 的 response，toast 提示 接口返回的 message || '请重试!'
 */
export const request = async <
  Result extends boolean | string | AnyObject | ArrayBuffer = AnyObject
>(
  options: Request.IRequestOption,
): Promise<Request.IResponseResult<Result>> => {
  try {
    const [res] = await Promise.all([
      baseRequest<Result>(options),
      delay(options?.delayResponse ?? 0),
    ]);

    if (!isRequestSuccess(res.data?.code) && !options.ignoreApiError) {
      showToast({
        title: res.data.message || '请重试!',
      });

      if (!isProdEnv) {
        // console.log(
        //   `request:\t${options.url}  ApiError: \n`,
        //   JSON.stringify(res, null, 2),
        // );
      }
    }
    res.data.isSuccess = isRequestSuccess(res.data?.code);
    return res.data;
  } catch (err: any) {
    if (!options.ignoreApiError) {
      showToast({
        title: err.message || '请重试',
      });
    }

    if (!isProdEnv) {
      // console.log(
      //   `request:\t${options.url}  ResponseError: \n`,
      //   JSON.stringify(err, null, 2),
      // );
    }

    return {
      isSuccess: false,
      code: -1,
      data: null as unknown as Result,
      message: err?.errMsg || err?.message || '',
    };
  }
};