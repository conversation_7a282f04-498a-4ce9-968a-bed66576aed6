<!-- 账号注销第二步：验证身份页面 -->
<view class="accountCancellation-step2-page">
  <view class="top">
    <view class="first step">
      <image class="step-icon" src="{{infoIcon}}"></image>
      <view class="step-content">
        <text class="step-title">第1步</text>
        <text class="step-tip">注意事项</text>
      </view>
    </view>
    <image class="account-arrow-icon" src="{{accountArrowIcon}}"></image>
    <view class="second step act">
      <image class="step-icon" src="{{accountIcon}}"></image>
      <view class="step-content">
        <text class="step-title">第2步</text>
        <text class="step-tip">验证身份</text>
      </view>
    </view>
  </view>
  
  <view class="verify">
    <view class="verify-title">
      <text class="verify-title-text">验证码已发送至183****9988</text>
    </view>
    <view class="verify-code" bindtap="onTapCodeBox">
      <block wx:for="{{[0,1,2,3,4,5]}}" wx:key="index">
        <view class="code-box {{code.length === index ? 'active' : ''}}">
          <text>{{ code[index] ? code[index] : '' }}</text>
          <view wx:if="{{focus && code.length === index}}" class="code-cursor"></view>
        </view>
      </block>
      <input class="code-input" type="number" maxlength="6" value="{{code}}" focus="{{focus}}" bindinput="onInput" bindfocus="onFocus" bindblur="onBlur" />
    </view>
    <view class="verify-countdown">
      <text class="verify-countdown-text">{{countdown > 0 ? countdown + 's后重新获取' : '重新获取'}}</text>
    </view>
  </view>
  
  <view class="info-tip">
    <text class="info-tip-text">注销后账号无法找回，请谨慎操作!</text>
  </view>
  
  <view class="submit">
    <text class="submit-btn" bindtap="onSubmit">确认注销</text>
  </view>
</view>
