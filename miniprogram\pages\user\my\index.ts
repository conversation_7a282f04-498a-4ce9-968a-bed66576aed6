// pages/user/my/index.ts
import { getUserInfo } from '../../../api/my';
import { getImageUrl } from '../../../utils/images';
import { hasToken } from '../../../utils/userInfo';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 是否已登录
    isLogin: false,
    // 用户信息，包含昵称和头像
    // 头像为空时会使用默认头像
    userInfo: {
      nickname: '',
      avatar: ''
    },
    // 箭头图标，用于列表项右侧
    arrowIcon: getImageUrl('arrow.png'),
    // 页面头部背景图
    headBgIcon: getImageUrl('user/user_my_bg.png'),
    // 用户默认头像，当接口返回的头像为空时使用
    avatarDefaultIcon: getImageUrl('user/avatar_default.png'),
    // 订单状态列表，用于快速访问不同状态的订单
    orderTabs: [{
      icon: getImageUrl('user/all_order.png'),
      title: '全部订单',
      id: 'all_order'
    }, {
      icon: getImageUrl('user/pending_payment.png'),
      title: '待支付',
      id: 'pending_payment'
    }, {
      icon: getImageUrl('user/not_traveling_yet.png'),
      title: '未出行',
      id: 'not_traveling_yet'
    }, {
      icon: getImageUrl('user/refund.png'),
      title: '退款/售后',
      id: 'refund'
    }],
    // 用户菜单列表，包含收藏、反馈、设置等功能入口
    userMenus: [{
      icon: getImageUrl('user/my_collection.png'),
      title: '我的收藏',
      path: '/pages/user/collection/index'
    }, {
      icon: getImageUrl('user/feedback.png'),
      title: '意见反馈',
      path: '/pages/user/feedback/index'
    }, {
      icon: getImageUrl('user/seting.png'),
      title: '设置',
      path: '/pages/user/seting/index'
    }]
  },
  /**
   * 初始化页面数据
   * 获取用户信息并更新到页面数据中
   * 如果获取失败则直接返回
   * 如果头像为空则使用默认头像
   */
  async init() {
    // 调用获取用户信息接口
    const { isSuccess, data } = await getUserInfo();
    // 接口调用失败，直接返回
    if (!isSuccess) return;
    // 解构获取用户头像和昵称，如果 data 为空则使用空对象
    const { avatar, nickname } = data || {}
    // 更新页面数据
    // 如果头像为空（null、undefined 或空字符串），则使用默认头像
    this.setData({
      userInfo: {
        avatar: avatar || this.data.avatarDefaultIcon,
        nickname
      },
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 判断有没有token，没有token直接提示未登录
    if (!hasToken()) return;
    // 有token走接口验证逻辑
    this.setData({
      isLogin: true
    })
    this.init()
  },

  /**
   * 处理菜单项点击事件
   * @param e 事件对象
   */
  onMenuClick(e: any) {
    const { path } = e.currentTarget.dataset;
    if (!path) return;

    // 特殊处理：跳转到设置页面，需事件监听
    if (path === '/pages/user/seting/index') {
      wx.navigateTo({
        url: path,
        events: {
          updateUserInfo: (data: My.UserInfoUpdateEvent) => {
            // 根据更新类型更新本地用户信息
            if (data.type === 'avatar' && data.avatar) {
              this.setData({
                'userInfo.avatar': data.avatar
              });
            } else if (data.type === 'nickname' && data.nickname) {
              this.setData({
                'userInfo.nickname': data.nickname
              });
            }
          }
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
      return;
    }

    // 其他页面正常跳转
    wx.navigateTo({
      url: path,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
})