import { request } from "../utils/request";

/**
 * 获取收银台基础数据
 */
export const getCashierInfo = () => {}

/**
 * 获取微信支付参数
 * @param data 请求参数
 */
export const getPaymentParams = (data: Pay.IGetPayParams) => request<Pay.IWxPayTrade<Pay.ITradeInfo>>({
  url: "/hotel/payment/lianLianPay/v1/invocationPayment",
  method: "POST",
  data
})

/**
 * 查询支付状态
 * @param orderNo 订单号
 */
export const queryPayStatus = (orderNo: string) => request<Pay.IWxPayTrade<Omit<Pay.ITradeInfo, "payload">>>({
  url: "/hotel/payment/lianLianQuery/v1/queryPaymentStatus",
  method: "POST",
  ignoreApiError: true,
  data: {
    orderNo
  }
});

/**
 * 获取支付信息
 * @param orderId 订单号
 */
export const getPaymentInfo = (orderId: string) => request<Pay.IPaymentInfo>({
  url: `/order/getOrderPayInfo?orderId=${orderId}`
})