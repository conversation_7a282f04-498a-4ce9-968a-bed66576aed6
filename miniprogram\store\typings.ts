/** 当前定位信息 */
export type GeoInfo = {
  /** 纬度 */
  lat: number;
  /** 经度 */
  lng: number;
  /** 最后更新时间（毫秒时间戳） */
  lastUpdatedTime?: number
};

export type State = {
  geoInfo: GeoInfo;
  cityInfo: {
    cityId: string;
    cityName: string;
  };
  userInfo: {
    openid: string;
    unionid: string;
  },
  token: string;
}
export type Actions = {
  setUserInfo: (payload: State['userInfo']) => void;
  setGeoInfo: (payload: State['geoInfo']) => void;
  setToken: (token: string) => void;
}

export type Store = State & Actions;