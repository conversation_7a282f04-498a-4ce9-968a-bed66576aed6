declare namespace API {
	namespace Common {
		interface PaginatingCommonParams {
			/** current page number */
			current: number;
			/** page size */
			size: number;
			/** total count */
			total: number;
		}

		/** common params of paginating query list data */
		interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
			records: T[];
		}

		type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'size'> & {
			page: number;
		};
	}
}