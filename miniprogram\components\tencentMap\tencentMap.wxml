<view class="map-container">
	<map
		id="map"
		style="height: calc(100% + 23px);width: 100%;"
		longitude="{{longitude}}"
		latitude="{{latitude}}"
		scale="{{scale}}"
		min-scale="{{minScale}}"
		max-scale="{{maxScale}}"
		show-scale="{{showScale}}"
		show-location="{{showLocation}}"
		enable-zoom="{{enableZoom}}"
		enable-scroll="{{enableScroll}}"
		enable-rotate="{{enableRotate}}"
		enable-poi="{{enablePoi}}"
		markers="{{markers}}"
		bindcallouttap="handleCalloutTap"
	>
		<cover-view slot="callout">
			<cover-view
				wx:for="{{markers}}"
				wx:key="id"
				marker-id="{{item.id}}"
				class="custom-callout-item {{item.active ? 'is-active': null}}"
			>{{item.customCallout.content}}</cover-view>
		</cover-view>

	</map>
</view>