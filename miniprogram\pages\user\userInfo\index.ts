import { getImageUrl } from "../../../utils/images";
import { GenderEnum, GenderTextEnum } from "../../../enum/index";
import { getUserInfo, updateUserInfo } from '../../../api/my';

/**
 * 用户信息更新事件类型
 * 用于页面间事件传递的用户信息更新类型
 * avatar事件只带avatar，nickname事件只带nickname
 * 这样类型推断更安全
 */
type UserInfoUpdateEvent =
  | ({ type: 'avatar' } & Pick<My.IUserInfoRes, 'avatar'>)
  | ({ type: 'nickname' } & Pick<My.IUserInfoRes, 'nickname'>);

/**
 * 生日选择器开始日期常量
 * 格式：YYYY-MM-DD
 */
const BIRTHDAY_START_DATE = '1900-01-01';

/**
 * 生日选择器结束日期常量
 * 默认为当前日期，格式：YYYY-MM-DD
 */
const BIRTHDAY_END_DATE = new Date().toISOString().split('T')[0];

/**
 * 性别显示信息接口
 * 包含性别文本和样式类名
 */
interface GenderDisplay {
  /** 性别显示文本 */
  text: string;
  /** 性别显示样式类名 */
  className: string;
}

/**
 * 性别配置映射
 * 根据性别枚举值获取对应的显示配置
 */
const GENDER_CONFIG = {
  [GenderEnum.Unknown]: {
    label: GenderTextEnum.Secret,
    text: GenderTextEnum.Secret,
    className: 'userInfo-text-empty'
  },
  [GenderEnum.Male]: {
    label: GenderTextEnum.Male,
    text: GenderTextEnum.Male,
    className: ''
  },
  [GenderEnum.Female]: {
    label: GenderTextEnum.Female,
    text: GenderTextEnum.Female,
    className: ''
  }
} as const;

// pages/user/userInfo/index.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    /** 用户信息数据，包含头像、昵称、性别、生日等 */
    userInfo: null as My.IUserInfoRes | null,
    /** 性别显示信息，包含文本和样式类名 */
    genderDisplay: { text: GenderTextEnum.Secret, className: 'userInfo-text-empty' } as GenderDisplay,
    /** 箭头图标，用于列表项右侧 */
    arrowIcon: getImageUrl('arrow.png'),
    /** 性别选择器是否可见 */
    genderPickerVisible: false,
    /** 性别选择器当前选中的值 */
    genderPickerValue: ['0'],
    /** 性别选择器选项列表 */
    genderOptions: [] as Array<{ label: string; value: string }>,
    /** 生日选择器是否可见 */
    birthdayPickerVisible: false,
    /** 生日选择器当前选中的值 */
    birthdayPickerValue: '',
    /** 生日选择器开始日期 */
    birthdayStartDate: BIRTHDAY_START_DATE,
    /** 生日选择器结束日期 */
    birthdayEndDate: BIRTHDAY_END_DATE,
    /** 昵称修改弹窗是否显示 */
    showNicknamePopup: false,
    /** 新昵称输入值 */
    newNickname: '',
    /** 昵称字符计数 */
    nicknameCharCount: 0,
  },

  /**
   * 检查值是否发生变化
   * @param field 字段名
   * @param newValue 新值
   * @returns 是否发生变化
   */
  isValueChanged(field: keyof My.IUserInfoRes, newValue: My.IUserInfoRes[keyof My.IUserInfoRes]): boolean {
    const currentValue = this.data.userInfo?.[field];
    return currentValue !== newValue;
  },

  /**
   * 统一处理用户信息更新
   * @param field 要更新的字段
   * @param newValue 新值
   * @param successCallback 成功回调函数
   * @param errorMessage 错误提示消息
   * @returns Promise<boolean> 更新是否成功
   */
  async updateUserInfoField(
    field: keyof My.IUpdateUserInfoReq,
    newValue: any,
    successCallback?: () => void,
    errorMessage: string = '更新失败'
  ): Promise<boolean> {
    try {
      // 调用更新用户信息接口
      const updateData = { [field]: newValue } as My.IUpdateUserInfoReq;
      const { isSuccess } = await updateUserInfo(updateData);

      // 隐藏加载提示
      wx.hideLoading();

      if (isSuccess) {
        // 接口调用成功，更新本地数据
        if (this.data.userInfo) {
          this.setData({
            [`userInfo.${field}`]: newValue
          });
        }
        
        // 执行成功回调
        if (successCallback) {
          successCallback();
        }
        
        return true;
      }
      
      // 接口调用失败，显示错误提示
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
      return false;
    } catch (error) {
      // 隐藏加载提示
      wx.hideLoading();
      console.error(`更新${field}失败:`, error);
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
      return false;
    }
  },

  /**
   * 向上传递用户信息更新事件
   * @param type 更新类型
   * @param data 更新数据
   */
  emitUserInfoUpdate(type: 'avatar' | 'nickname', data: any) {
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.emit('updateUserInfo', {
        type,
        ...data
      } as UserInfoUpdateEvent);
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 初始化性别选项
    this.initGenderOptions();
    this.init();
  },

  /**
   * 初始化页面数据
   * 获取用户信息并更新到页面数据中
   * 如果获取失败则直接返回
   */
  async init() {
    // 调用获取用户信息接口
    const { isSuccess, data } = await getUserInfo();
    // 接口调用失败，直接返回
    if (!isSuccess) {
      // 接口调用失败，不更新页面数据
      return;
    }
    // 更新页面数据
    this.setData({
      userInfo: data,
      genderDisplay: this.getGenderText(data.gender)
    });
  },

  /**
   * 初始化性别选项
   * 根据性别枚举生成选择器选项列表
   */
  initGenderOptions() {
    const genderOptions = Object.values(GenderEnum).map(gender => ({
      label: GENDER_CONFIG[gender].label,
      value: gender
    }));
    this.setData({
      genderOptions
    });
  },

  /**
   * 处理昵称点击事件，显示修改昵称弹窗
   * 初始化弹窗数据为当前昵称
   */
  onNicknameClick() {
    this.setData({
      showNicknamePopup: true,
      newNickname: this.data.userInfo?.nickname || '', // 初始化为当前昵称
      nicknameCharCount: (this.data.userInfo?.nickname || '').length,
    });
  },

  /**
   * 处理昵称输入事件
   * 更新新昵称值和字符计数
   * @param e 输入事件对象
   */
  onNicknameChange(e: any) {
    const value = e.detail.value;
    this.setData({
      newNickname: value,
      nicknameCharCount: value.length,
    });
  },

  /**
   * 处理保存昵称按钮点击事件
   * 验证昵称并调用更新接口，如果昵称相同则直接关闭弹窗
   */
  async onSaveNickname() {
    // 实际的保存逻辑，调用更新接口
    const nicknameToUpdate = this.data.newNickname.trim();
    // 昵称不能为空
    if (!nicknameToUpdate) {
      wx.showToast({ title: '昵称不能为空', icon: 'none' });
      return;
    }

    // 检查新昵称是否与当前昵称相同
    if (!this.isValueChanged('nickname', nicknameToUpdate)) {
      // 昵称相同，直接关闭弹窗
      this.setData({
        showNicknamePopup: false,
      });
      return;
    }

    // 使用封装方法更新昵称
    await this.updateUserInfoField(
      'nickname', 
      nicknameToUpdate, 
      () => {
        // 成功回调：关闭昵称弹窗并向上传递事件
        this.setData({
          showNicknamePopup: false,
        });
        
        // 向上传递昵称更新事件给seting页面
        this.emitUserInfoUpdate('nickname', { nickname: nicknameToUpdate });
      },
      '昵称修改失败'
    );
  },

  /**
   * 处理取消按钮点击事件或遮罩点击事件，关闭弹窗
   */
  onCancelNickname() {
    this.setData({
      showNicknamePopup: false,
    });
  },

  /**
   * 处理性别点击事件
   * 设置当前选中的性别索引并显示性别选择器
   */
  onGenderClick() {
    // 设置当前选中的性别索引
    const currentGender = this.data.userInfo?.gender || GenderEnum.Unknown;
    const genderIndex = this.data.genderOptions.findIndex(option => option.value === currentGender);

    // 先设置value，再显示picker
    this.setData({
      genderPickerValue: [String(genderIndex)]
    }, () => {
      this.setData({
        genderPickerVisible: true
      });
    });
  },

  /**
   * 处理性别选择器确认事件
   * 更新用户性别信息，如果选择相同则直接关闭选择器
   * @param e 选择器事件对象
   */
  async onGenderPickerConfirm(e: any) {
    const { value } = e.detail;
    const selectedIndex = parseInt(value[0]);
    const selectedGender = this.data.genderOptions[selectedIndex].value;

    // 如果选择的性别与当前性别相同，直接关闭选择器
    if (!this.isValueChanged('gender', selectedGender)) {
      this.setData({
        genderPickerValue: [String(selectedIndex)],
        genderPickerVisible: false
      });
      return;
    }

    // 使用封装方法更新性别
    await this.updateUserInfoField(
      'gender', 
      selectedGender, 
      () => {
        // 成功回调：更新性别显示信息并关闭选择器
        this.setData({
          genderDisplay: this.getGenderText(selectedGender),
          genderPickerValue: [String(selectedIndex)],
          genderPickerVisible: false
        });
      },
      '性别更新失败'
    );
  },

  /**
   * 处理性别选择器取消事件
   */
  onGenderPickerCancel() {
    this.setData({
      genderPickerVisible: false
    });
  },

  /**
   * 获取性别显示信息
   * 根据性别代码返回对应的显示文本和样式类名
   * @param gender 性别代码
   * @returns 性别显示信息对象
   */
  getGenderText(gender: string | number | undefined | null): GenderDisplay {
    // 转换为字符串进行比较
    const genderStr = String(gender);

    // 使用配置映射获取性别信息
    const genderInfo = GENDER_CONFIG[genderStr as GenderEnum];
    if (genderInfo) {
      // 找到对应的性别配置，返回配置信息
      return {
        text: genderInfo.text,
        className: genderInfo.className
      };
    }
    // 未找到对应的性别配置，返回默认保密信息
    return {
      text: GenderTextEnum.Secret,
      className: GENDER_CONFIG[GenderEnum.Unknown].className
    };
  },

  /**
   * 处理生日点击事件
   * 设置当前生日为默认值并显示生日选择器
   */
  onBirthdayClick() {
    // 设置当前生日为默认值，如果没有则使用空字符串
    const currentBirthday = this.data.userInfo?.birthday || '';
    this.setData({
      birthdayPickerVisible: true,
      birthdayPickerValue: currentBirthday
    });
  },

  /**
   * 处理生日选择器确认事件
   * 更新用户生日信息，如果选择相同则直接关闭选择器
   * @param e 选择器事件对象
   */
  async onBirthdayPickerConfirm(e: any) {
    const { value } = e.detail;
    // 截取日期部分，去掉时间部分
    const selectedBirthday = value.split(' ')[0];
    
    // 如果选择的生日与当前生日相同，直接关闭选择器
    if (!this.isValueChanged('birthday', selectedBirthday)) {
      this.setData({
        birthdayPickerValue: selectedBirthday,
        birthdayPickerVisible: false
      });
      return;
    }

    // 使用封装方法更新生日
    await this.updateUserInfoField(
      'birthday', 
      selectedBirthday, 
      () => {
        // 成功回调：关闭生日选择器
        this.setData({
          birthdayPickerValue: selectedBirthday,
          birthdayPickerVisible: false
        });
      },
      '生日更新失败'
    );
  },

  /**
   * 处理生日选择器取消事件
   */
  onBirthdayPickerCancel() {
    this.setData({
      birthdayPickerVisible: false
    });
  },

  /**
   * 处理头像点击事件
   * 跳转到头像选择页面，并监听头像更新事件
   */
  onAvatarClick() {
    // 获取当前头像地址
    const currentAvatar = this.data.userInfo?.avatar || '';
    wx.navigateTo({
      url: `/pages/user/chooseAvatar/index?avatar=${encodeURIComponent(currentAvatar)}`,
      events: {
        /**
         * 监听头像更新事件
         * @param data 头像更新数据，包含新的头像地址
         */
        updateAvatar: (data: { avatar: string }) => {
          // 更新当前页面的头像
          if (this.data.userInfo) {
            // 用户信息存在，更新头像
            this.setData({
              'userInfo.avatar': data.avatar
            });
          }
          // 向上传递头像更新事件给seting页面
          this.emitUserInfoUpdate('avatar', { avatar: data.avatar });
        }
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
})