import { getImageUrl } from "../../../utils/images";

// pages/user/feedback/index.ts
Page({
  data: {
    // 反馈类型选项
    feedbackTypes: [
      { label: '功能故障', value: 'bug' },
      { label: '产品建议', value: 'suggestion' },
      { label: '订单问题', value: 'order' },
      { label: '其他反馈', value: 'other' }
    ],
    selectedType: '', // 选中的反馈类型
    feedbackContent: '', // 反馈内容
    uploadedImages: [] as string[], // 已上传的图片列表
    canSubmit: false, // 是否可以提交
    pictureIcon: getImageUrl('user/picture.png'),
    feedbackCloseIcon: getImageUrl('user/feedback_close.png'),
  },

  /**
   * 选择反馈类型
   */
  onSelectType(e: any) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      selectedType: value
    });
    this.checkCanSubmit();
  },

  /**
   * 输入反馈内容
   */
  onContentInput(e: any) {
    const value = e.detail.value;
    this.setData({
      feedbackContent: value
    });
    this.checkCanSubmit();
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    const remainingCount = 9 - this.data.uploadedImages.length;
    
    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFiles.map(file => file.tempFilePath);
        this.setData({
          uploadedImages: [...this.data.uploadedImages, ...tempFilePaths]
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除图片
   */
  onDeleteImage(e: any) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.uploadedImages];
    images.splice(index, 1);
    this.setData({
      uploadedImages: images
    });
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const canSubmit = this.data.selectedType && this.data.feedbackContent.trim();
    this.setData({
      canSubmit: !!canSubmit
    });
  },

  /**
   * 提交反馈
   */
  onSubmit() {
    if (!this.data.canSubmit) {
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交中...'
    });

    // 这里可以添加实际的提交逻辑
    // 比如上传图片到服务器，然后提交反馈数据
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      // 提交成功后返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  }
})
