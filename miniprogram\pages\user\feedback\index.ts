import { getImageUrl } from "../../../utils/images";

import { uploadFeedbackImage, submitFeedback, uploadMultipleFeedbackImagesBase64, uploadMultipleFeedbackImagesFormData } from "../../../api/my";
import { EFeedbackType } from "../../../enum/my";

// pages/user/feedback/index.ts
Page({
  data: {
    // 反馈类型选项
    feedbackTypes: [
      { label: '功能故障', value: 'bug' },
      { label: '产品建议', value: 'suggestion' },
      { label: '订单问题', value: 'order' },
      { label: '其他反馈', value: 'other' }
    ],
    selectedType: '', // 选中的反馈类型
    feedbackContent: '', // 反馈内容
    uploadedImages: [] as string[], // 已上传的图片列表（本地路径）
    uploadedImageUrls: [] as string[], // 已上传的图片URL列表（服务器返回）
    canSubmit: false, // 是否可以提交
    uploading: false, // 是否正在上传图片
    pictureIcon: getImageUrl('user/picture.png'),
    feedbackCloseIcon: getImageUrl('user/feedback_close.png'),
  },

  /**
   * 选择反馈类型
   */
  onSelectType(e: any) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      selectedType: value
    });
    this.checkCanSubmit();
  },

  /**
   * 输入反馈内容
   */
  onContentInput(e: any) {
    const value = e.detail.value;
    this.setData({
      feedbackContent: value
    });
    this.checkCanSubmit();
  },

  /**
   * 选择图片并上传
   */
  onChooseImage() {
    const remainingCount = 9 - this.data.uploadedImages.length;

    if (this.data.uploading) {
      wx.showToast({
        title: '正在上传中，请稍候',
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 逐个上传选中的图片
        console.log('=== 选择图片成功 ===');
        console.log('选择的图片数量:', res.tempFiles.length);
        console.log('图片路径列表:', res.tempFiles.map(file => file.tempFilePath));

        const filePaths = res.tempFiles.map(file => file.tempFilePath);
        this.uploadImages(filePaths);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 批量上传图片（一次上传多张）
   */
  async uploadImages(filePaths: string[]) {
    console.log('=== 开始批量上传图片 ===');
    console.log('待上传图片数量:', filePaths.length);
    console.log('待上传图片路径:', filePaths);

    this.setData({ uploading: true });

    try {
      // 显示上传进度
      wx.showLoading({
        title: `上传中 (${filePaths.length}张图片)`
      });

      // 尝试方案1：Base64方式上传
      console.log('=== 尝试Base64方式上传 ===');
      let result = await uploadMultipleFeedbackImagesBase64(filePaths);

      // 如果Base64方式失败，尝试FormData方式
      if (!result.isSuccess) {
        console.log('=== Base64方式失败，尝试FormData方式 ===');
        result = await uploadMultipleFeedbackImagesFormData(filePaths);
      }

      // 如果都失败，回退到逐个上传
      if (!result.isSuccess) {
        console.log('=== 多文件上传失败，回退到逐个上传 ===');
        wx.hideLoading();
        await this.uploadImagesOneByOne(filePaths);
        return;
      }

      console.log('多文件上传结果:', result);

      if (result.isSuccess && result.data) {
        // 上传成功，处理返回的URL数组
        const urls = Array.isArray(result.data) ? result.data : [result.data];

        console.log('上传成功，获得URLs:', urls);

        // 更新状态
        this.setData({
          uploadedImages: [...this.data.uploadedImages, ...filePaths],
          uploadedImageUrls: [...this.data.uploadedImageUrls, ...urls]
        });

        wx.showToast({
          title: `成功上传${urls.length}张图片`,
          icon: 'success'
        });
      } else {
        // 上传失败
        console.error('多文件上传失败:', result);
        wx.showToast({
          title: result.message || '图片上传失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('上传图片异常:', error);
      wx.showToast({
        title: '图片上传失败',
        icon: 'none'
      });
    }

    wx.hideLoading();
    this.setData({ uploading: false });
  },

  /**
   * 回退方案：逐个上传图片
   */
  async uploadImagesOneByOne(filePaths: string[]) {
    console.log('=== 开始逐个上传图片 ===');

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];
      try {
        // 显示上传进度
        wx.showLoading({
          title: `上传中 ${i + 1}/${filePaths.length}`
        });

        // 调用单文件上传接口
        const { isSuccess, data, message } = await uploadFeedbackImage(filePath);

        if (isSuccess && data) {
          // 处理返回的URL
          let imageUrl = '';
          if (Array.isArray(data) && data.length > 0) {
            imageUrl = data[0];
          } else if (data.url) {
            imageUrl = data.url;
          } else if (typeof data === 'string') {
            imageUrl = data;
          }

          if (imageUrl) {
            this.setData({
              uploadedImages: [...this.data.uploadedImages, filePath],
              uploadedImageUrls: [...this.data.uploadedImageUrls, imageUrl]
            });
          }
        } else {
          wx.showToast({
            title: message || '图片上传失败',
            icon: 'none'
          });
          break;
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
        break;
      }
    }
  },

  /**
   * 删除图片
   */
  onDeleteImage(e: any) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.uploadedImages];
    const imageUrls = [...this.data.uploadedImageUrls];

    images.splice(index, 1);
    imageUrls.splice(index, 1);

    this.setData({
      uploadedImages: images,
      uploadedImageUrls: imageUrls
    });
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const canSubmit = this.data.selectedType && this.data.feedbackContent.trim();
    this.setData({
      canSubmit: !!canSubmit
    });
  },

  /**
   * 提交反馈
   */
  async onSubmit() {
    if (!this.data.canSubmit) {
      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '提交中...'
    });

    try {
      // 构造提交数据
      const feedbackData = {
        type: this.getFeedbackTypeNumber(this.data.selectedType),
        content: this.data.feedbackContent.trim(),
        images: this.data.uploadedImageUrls.length > 0 ? this.data.uploadedImageUrls : undefined
      };

      // 调用提交接口
      const { isSuccess, data, message } = await submitFeedback(feedbackData);

      wx.hideLoading();

      if (isSuccess && data) {
        // 提交成功
        wx.showModal({
          title: '提交成功',
          content: `您的反馈已提交成功，反馈ID：${data.feedbackId}`,
          showCancel: false,
          success: () => {
            // 返回上一页
            wx.navigateBack();
          }
        });
      } else {
        // 提交失败
        wx.showModal({
          title: '提交失败',
          content: message || '提交失败，请重试',
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交反馈失败:', error);
      wx.showModal({
        title: '提交失败',
        content: '网络异常，请稍后重试',
        showCancel: false
      });
    }
  },

  /**
   * 将反馈类型字符串转换为枚举值
   */
  getFeedbackTypeNumber(type: string): number {
    const typeMap: Record<string, EFeedbackType> = {
      'bug': EFeedbackType.BUG,              // 功能故障
      'suggestion': EFeedbackType.SUGGESTION, // 产品建议
      'order': EFeedbackType.ORDER,          // 订单问题
      'other': EFeedbackType.OTHER           // 其他反馈
    };
    return typeMap[type] || EFeedbackType.OTHER;
  }
})
