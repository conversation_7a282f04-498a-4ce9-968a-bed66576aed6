<view class="cashier-page">
  <view class="merchant-container">
    <text class="pay-to">付款给</text>
    <view class="merchant-info">
      <view class="merchant-sign">
        商户
      </view>
      <view>
        {{paymentInfo.companyName}}
      </view>
    </view>
  </view>
  <view class="payment-container">
    <view class="payment-info">
      <view class="payment-title">
        消费金额
      </view>
      <view class="payment-amount">
        <text class="currency">￥</text>
        <text>{{paymentInfo.totalActualPrice}}</text>
      </view>
    </view>
    <button class="payment-btn" type="primary" bindtap="handlePayment" loading="{{payLoading}}" disabled="{{!paymentInfo.totalActualPrice || payLoading}}">确认支付</button>
  </view>
</view>

<md-loading visible="{{loading}}" />

<demo />
