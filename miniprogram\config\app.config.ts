const { env, version } = (function () {
  const {
    miniProgram: { envVersion, version: _version },
  } = wx.getAccountInfoSync();

  if (envVersion === 'develop' || envVersion === 'trial') {
    return {
      env: 'dev',
      version: _version,
    };
  }

  return { env: 'prod', version: _version };
})();

const config: Record<typeof env, string> = {
  dev: 'http://api.test.aitrip123.com',
  prod: '',
};

const appConfig = {
  baseUrl: config[env],
  imagesDomains: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota',
  defaultHeaders: {
    /** 小程序渠道标识 */
    'HT-Platform': 3,
    'HT-AppVersion': version || '1.0.0',
    // "HT-SystemVersion": ""
  },
  requestTimeout: 10000,
  /** 运行环境 */
  env,
  /** 小程序 AppId */
  appId: 'wxa3f0598a2ec6456e',
};

// console.info('appConfig:\n', appConfig);

export default appConfig;
