import type { IMapData, IMapProperty, IMapMethod, IMapCustomProperty, IMarker } from "./types";

const defaultLocation = {
  longitude: 120.0026066080729,
  latitude: 30.27960611979167
}

Component<IMapData, IMapProperty, IMapMethod, WechatMiniprogram.Component.BehaviorOption, IMapCustomProperty>({

  /**
   * 组件的属性列表
   */
  properties: {
    longitude: {
      type: Number,
      value: defaultLocation.longitude
    },
    latitude: {
      type: Number,
      value: defaultLocation.latitude
    },
    scale: {
      type: Number,
      value: 16
    },
    minScale: {
      type: Number,
      value: 3
    },
    maxScale: {
      type: Number,
      value: 20
    },
    showScale: {
      type: Boolean,
      value: false
    },
    showLocation: {
      type: Boolean,
      value: false
    },
    enableZoom: {
      type: Boolean,
      value: true
    },
    enableScroll: {
      type: Boolean,
      value: true
    },
    enableRotate: {
      type: Boolean,
      value: false
    },
    enablePoi: {
      type: <PERSON><PERSON><PERSON>,
      value: true
    }
  },

  customInstanceProperty: {
    __mapVm: null
  },

  /**
   * 组件的初始数据
   */
  data: {
    markers: []
  },

  lifetimes: {
    ready() {
      this.__mapVm = wx.createMapContext('map', this);
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleCalloutTap(e) {
      this.triggerEvent('callouttap', { markerId: e.detail.markerId });
    },
    setMarkers(options) {
      let markerList: IMarker[] = options.markers.map(item => ({
        callout: {
          display: 'ALWAYS',
          content: `￥${item.price}起`,
          bgColor: '#fff',
          color: '#333',
          borderWidth: 1,
          borderColor: '#fff',
          padding: 5,
          borderRadius: 5
        },
        ...item,
        width: 0,
        height: 0,
        rotate: 0,
        alpha: 0
      }));
      this.setData({
        markers: options.clear ? markerList : this.data.markers.concat(...markerList)
      });
    },
    setMarkersActive({ markerIds, clear }) {
      const markers = this.data.markers;
      markers.forEach((item) => {
        if (markerIds.includes(item.id)) {
          item.active = true;
          if (item.callout) {
            item.callout = {
              ...item.callout,
              bgColor: '#568DED',
              color: '#fff',
            }
          }
        } else if (clear) {
          item.active = false;
          if (item.callout) {
            item.callout = {
              ...item.callout,
              bgColor: '#fff',
              color: '#333',
            }
          }
        }
      })
      this.setData({
        markers
      });
    }
  }
})