<!-- 账号注销页面 -->
<view class="accountCancellation-my-page">
  <view class="top">
    <view class="first step{{step === 1 ? ' act' : ''}}">
      <image class="step-icon" src="{{infoIcon}}"></image>
      <view class="step-content">
        <text class="step-title">第1步</text>
        <text class="step-tip">注意事项</text>
      </view>
    </view>
    <image class="account-arrow-icon" src="{{accountArrowIcon}}"></image>
    <view class="second step{{step === 2 ? ' act' : ''}}">
      <image class="step-icon" src="{{accountIcon}}"></image>
      <view class="step-content">
        <text class="step-title">第2步</text>
        <text class="step-tip">验证身份</text>
      </view>
    </view>
  </view>
  <view wx:if="{{step === 1}}">
    <view class="precautions">
      <text class="precautions-text">为保障您的账号安全，需注意注销账号后账号内所有内容及订单也将删除，无法恢复、需满足以下条件才可注销账号: </text>
      <text class="precautions-text first-child">账号交易已结清账号中无第三方未完成或存在争议的服务</text>
      <text class="precautions-text second-child" >账号处于安全状态无任何账户异常、被限制记录</text>
    </view>
    <view class="agreements">
      <image class="agreements-icon" src="{{agreementChecked ? circleActIcon : circleBlueIcon}}" bindtap="onToggleAgreement"></image>
      <text class="agreements-title">已阅读并同意</text>
      <text class="agreements-btn">《注销协议》</text>
    </view>
    <view class="next">
      <text class="next-btn" bindtap="onNextStep">下一步</text>
    </view>
  </view>
  <view wx:elif="{{step === 2}}">
    <view class="verify">
      <view class="verify-title">
        <text class="verify-title-text">验证码已发送至183****9988</text>
      </view>
      <view class="verify-code" bindtap="onTapCodeBox">
        <block wx:for="{{[0,1,2,3,4,5]}}" wx:key="index">
          <view class="code-box {{code.length === index ? 'active' : ''}}">
            <text>{{ code[index] ? code[index] : '' }}</text>
            <view wx:if="{{focus && code.length === index}}" class="code-cursor"></view>
          </view>
        </block>
        <input class="code-input" type="number" maxlength="6" value="{{code}}" focus="{{focus}}" bindinput="onInput" bindfocus="onFocus" bindblur="onBlur" />
      </view>
      <view class="verify-countdown">
        <text class="verify-countdown-text">59s后重新获取</text>
      </view>
    </view>
    <view class="info-tip">
      <text class="info-tip-text">注销后账号无法找回，请谨慎操作!</text>
    </view>
    <view class="submit">
      <text class="submit-btn" bindtap="onSubmit">确认注销</text>
    </view>
  </view>
</view>