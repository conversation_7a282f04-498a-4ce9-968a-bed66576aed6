# Filter 筛选组件使用说明

## 组件功能

- 支持四个筛选项：智能排序、价格/星级、位置距离、筛选
- 智能排序显示当前选项文本
- 其他三项显示选中数量徽标
- 有选中项时自动高亮
- 点击时箭头旋转动画
- 完整的事件通信接口

## 使用方法

### 1. 在页面中引入组件

```json
// hotelList.json
{
  "usingComponents": {
    "filter": "./components/Filter/Filter"
  }
}
```

### 2. 在页面中使用

```xml
<!-- hotelList.wxml -->
<filter
  current-sort-label="{{filterData.currentSortLabel}}"
  price-star-count="{{filterData.priceStarCount}}"
  distance-count="{{filterData.distanceCount}}"
  filter-count="{{filterData.filterCount}}"
  bind:sortClick="onSortClick"
  bind:priceStarClick="onPriceStarClick"
  bind:distanceClick="onDistanceClick"
  bind:filterClick="onFilterClick"
/>
```

### 3. 页面数据配置

```typescript
// hotelList.ts
data: {
  filterData: {
    currentSortLabel: '智能排序',  // 排序显示文本
    priceStarCount: 0,           // 价格/星级选中数量
    distanceCount: 2,            // 位置距离选中数量
    filterCount: 1               // 筛选选中数量
  }
}
```

## 测试案例

### 测试案例 1：初始状态

```javascript
// 数据配置
filterData: {
  currentSortLabel: '智能排序',
  priceStarCount: 0,
  distanceCount: 0,
  filterCount: 0
}
// 预期效果：所有按钮默认状态，无高亮，无数量显示
```

### 测试案例 2：有选中项状态

```javascript
// 数据配置
filterData: {
  currentSortLabel: '价格最低',
  priceStarCount: 2,
  distanceCount: 1,
  filterCount: 3
}
// 预期效果：
// - 智能排序显示"价格最低"，高亮
// - 价格/星级显示数量"2"，高亮
// - 位置距离显示数量"1"，高亮
// - 筛选显示数量"3"，高亮
```

### 测试案例 3：部分选中状态

```javascript
// 数据配置
filterData: {
  currentSortLabel: '智能排序',
  priceStarCount: 0,
  distanceCount: 0,
  filterCount: 5
}
// 预期效果：只有筛选按钮高亮并显示数量"5"
```

### 测试案例 4：点击交互测试

- 点击任意按钮，箭头应旋转 180 度
- 高亮状态的按钮，箭头和文字为主题蓝色
- 再次点击同一按钮，箭头恢复原状
- 触发对应的事件回调

## 事件回调数据格式

```typescript
// 排序点击事件
onSortClick(event) {
  console.log(event.detail);
  // 输出：{ expanded: true/false, currentLabel: '智能排序', options: [] }
}

// 价格/星级点击事件
onPriceStarClick(event) {
  console.log(event.detail);
  // 输出：{ expanded: true/false, count: 2, options: [] }
}

// 位置距离点击事件
onDistanceClick(event) {
  console.log(event.detail);
  // 输出：{ expanded: true/false, count: 1, options: [] }
}

// 筛选点击事件
onFilterClick(event) {
  console.log(event.detail);
  // 输出：{ expanded: true/false, count: 3, options: [] }
}
```

## 样式自定义

组件支持通过 CSS 变量自定义主题色：

```css
.filter-container {
  --primary-color: #0052d9; /* 主题色 */
  --text-color: #333; /* 文字颜色 */
  --border-color: #f0f0f0; /* 边框颜色 */
  --bg-color: #fff; /* 背景色 */
}
```
