<map style="width: 100vw;height: 40vh" class="map" markers="{{markers}}" latitude="{{location.latitude}}" longitude="{{location.longitude}}" scale="{{scale}}" show-location bindcallouttap="handleCalloutTap">
  <cover-view slot="callout">
    <cover-view wx:for="{{markers}}" wx:key='id' marker-id="{{item.id}}" class="custom-callout-item {{item.active ? 'is-active': null}}">{{item.customCallout.content}}</cover-view>
  </cover-view>
</map>