declare namespace Hotel {

  // 酒店列表入参
  interface IHotelListParams {
    queryText: string;
    cityId: string;
    arrivalDate: string;
    departureDate: string;
    filters: Array<IFilter>;
    sort: string | null;
    pageIndex?: number;
    pageSize?: number;
  }
  interface IFilters {
    typeId: number;
    filterId: string;
  }
  // 酒店列表出参
  interface IHotelListResponse {
    hasMore: boolean;
    hotels: Array<IHotelList>;
  }
  interface IHotelList {
    hotelId: string;
    lowRate: string;
    currencyCode: string;
    poiName: string;
    //千米
    distance: number;
    //酒店是否失效(用于用户行为酒店列表)
    hotelInvalid: boolean;
    facilities: string;
    hotel: IHotel;
  }
  interface IHotel {
    hotelName: string;
    starRate: number;
    latitude: string;
    longitude: string;
    category: number;
    address: string;
    phone: string;
    thumbNailUrl: string;
    city: string;
    cityName: string;
    district: string;
    districtName: string;
    businessZone: string;
    businessZoneName: string;
    review: {
      good: number;
      poor: number;
      count: number;
      score: number;
    };
    checkInTime: string;
    checkOutTime: string;
    features: string;
    generalAmenities: string;
    traffic: string;
    description: string;
  }

  // 酒店排序方式出参
  interface ISortTypeList {
    sort: Array<ISortType>;
    category: ICategoryMap;
    star: IStarMap;
    selectItemType: ISelectItemTypeMap;
  }
  
  interface ISortType {
    key: string;
    desc: string;
  }

  // 酒店分类映射（数字键对应分类名称）
  interface ICategoryMap {
    [key: string]: string;
  }

  // 酒店星级映射（数字键对应星级描述）
  interface IStarMap {
    [key: string]: string;
  }

  // 选择项类型映射（数字键对应类型描述）
  interface ISelectItemTypeMap {
    [key: string]: string;
  }

  // 价格和星级筛选响应
  interface IPriceStarFilterResponse {
    priceRangeVOs: Array<IPriceRangeVO>;
    starRatingVOs: Array<IStarRatingVO>;
  }

  // 价格区间VO
  interface IPriceRangeVO {
    text: string;
    low: number;
    high?: number; // 可选，如"￥1700以上"没有上限
  }

  // 星级评级VO
  interface IStarRatingVO {
    desc: string;
    text: string;
    vals: Array<number>;
  }

  // 区域筛选入参
  interface IAreaFilterParams {
    cityId: string;
    typeId?: number | null;
  }


  // 通用筛选器接口（用于区域筛选、筛选选项等场景）
  interface IFilter {
    typeId: number;
    filterId: string;
    name: string;
    nameEn: string | null;
    describe: string | null;
    multi: number;
    subFilters: Array<IFilter>;
    grey: any | null; // grey字段类型不明确，使用any
  }

  // 区域筛选响应
  type IAreaFilterResponse = Array<IFilter>;

  // 筛选选项入参
  interface IFilterOptionsParams {
    cityId: string;
  }

  // 筛选选项响应
  type IFilterOptionsResponse = Array<IFilter>;
  
}

