declare namespace Hotel {
  interface IHotelListParams {
    queryText: string;
    cityId: string;
    arrivalDate: string;
    departureDate: string;
  }
  interface IHotelListResponse {
    hasMore: boolean;
    hotels: Array<IHotelList>;
  }
  interface IHotelList {
    hotelId: string;
    lowRate: string;
    currencyCode: string;
    poiName: string;
    //千米
    distance: number;
    //酒店是否失效(用于用户行为酒店列表)
    hotelInvalid: boolean;
    facilities: string;
    hotel: IHotel;
  }
  interface IHotel {
    hotelName: string;
    starRate: number;
    latitude: string;
    longitude: string;
    category: number;
    address: string;
    phone: string;
    thumbNailUrl: string;
    city: string;
    cityName: string;
    district: string;
    districtName: string;
    businessZone: string;
    businessZoneName: string;
    review: {
      good: number;
      poor: number;
      count: number;
      score: number;
    };
    checkInTime: string;
    checkOutTime: string;
    features: string;
    generalAmenities: string;
    traffic: string;
    description: string;
  }
}
