<view class="navbar-container" style="height: {{navBarHeight + statusBarHeight}}px;">
  <view class="navbar-placeholder" style="height: {{statusBarHeight}}px;"></view>
  <view class="navbar-content" style="height: {{navBarHeight}}px; padding-right: {{menuPaddingRight}}px;">
    <!-- 左侧返回按钮 -->
    <view class="navbar-left" style="height: {{menuButtonHeight}}px; margin-top: {{(navBarHeight - menuButtonHeight) / 2}}px;">
      <image  bindtap="handleBack" class="navbar-back-icon" src="../../assets/backIcon.png" mode="aspectFit|aspectFill|widthFix"  />
    </view>
    
    <!-- 中间城市选择区域 -->
    <view class="navbar-center" bindtap="handleCitySelect" style="height: {{menuButtonHeight}}px; margin-top: {{(navBarHeight - menuButtonHeight) / 2}}px;">
      <view class="navbar-city-wrapper">
        <text class="navbar-city-name">{{cityName}}</text>
        <view class="navbar-city-arrow">
        </view>
      </view>
    </view>
    
    <!-- 右侧留空，与胶囊按钮对齐 -->
    <view class="navbar-right"></view>
  </view>
</view> 