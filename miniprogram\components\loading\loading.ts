import Toast, { hideToast, ToastOptionsType } from '../../miniprogram_npm/tdesign-miniprogram/toast/index';

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false,
    },
    toastProps: {
      type: Object,
      value: {
        message: '加载中...',
        theme: 'loading',
        duration: -1,
        direction: 'column',
      } as ToastOptionsType,
    },
    hideToastProps: {
      type: Object,
      value: {} as ToastOptionsType,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {},
  observers: {
    visible() {
      const visible = this.data.visible;
      if (visible) {
        Toast({
          context: this,
          selector: '#t-toast',
          ...this.data.toastProps,
        });
      } else {
        hideToast({
          context: this,
          selector: '#t-toast',
          ...this.data.hideToastProps,
        });
      }
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {

  },
});
