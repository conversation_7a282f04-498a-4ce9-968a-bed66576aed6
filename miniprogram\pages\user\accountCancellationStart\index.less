/* 账号注销开始页面样式 */
.accountCancellation-start-page {
  .top {
    margin: 40rpx 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .account-arrow-icon {
      width: 44rpx;
      height: 44rpx;
    }
    .step {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      padding: 28rpx 24rpx;
      border-radius: 48rpx 48rpx 48rpx 48rpx;
      &.act {
        background-color: #568ded;
        .step-content {
          .step-title {
            color: #fff;
          }
          .step-tip {
            color: #fff;
          }
        }
      }
      .step-icon {
        width: 36rpx;
        height: 36rpx;
        background-color: #f7f7f7;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 18rpx;
      }
      .step-content {
        margin-left: 16rpx;
        .step-title {
          display: block;
          color: #000;
          font-size: 24rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
        }
        .step-tip {
          display: block;
          color: #66666e;
          font-size: 24rpx;
        }
      }
    }
  }
  .precautions {
    margin: 48rpx 16rpx;
    padding: 32rpx;
    background-color: #fff;
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    .precautions-text {
      display: block;
      color: #000;
      font-size: 28rpx;
      line-height: 40rpx;
      &:first-child {
        margin-bottom: 24rpx;
      }
      &:not(:first-child) {
        padding-left: 40rpx;
        padding-right: 10rpx;
        position: relative;
        &.first-child::before {
          content: "1.";
          position: absolute;
          left: 5rpx;
        }
        &.second-child::before {
          content: "2.";
          position: absolute;
          left: 5rpx;
        }
      }
    }
  }
  .agreements {
    margin: 188rpx 0 0;
    display: flex;
    justify-content: center;
    width: 100%;
    align-items: center;
    .agreements-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 16rpx;
    }
    .agreements-title {
      color: #000;
      font-size: 28rpx;
      margin-right: 16rpx;
    }
    .agreements-btn {
      color: #568ded;
      font-size: 28rpx;
    }
  }
  .next {
    margin: 16rpx;
    height: 112rpx;
    line-height: 112rpx;
    text-align: center;
    background: #568ded;
    border-radius: 120rpx;
    .next-btn {
      color: #fff;
      font-size: 32rpx;
    }
  }
}
